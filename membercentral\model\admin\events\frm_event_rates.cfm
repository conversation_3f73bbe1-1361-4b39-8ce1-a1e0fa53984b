<cfsavecontent variable="local.ratesJS">
	<cfoutput>
	<!--- GL Account Widget JavaScript --->
	#local.strRevenueGLAcctWidget.js#
	<script language="javascript">
	let evRatesTable;
	let managePaidRates = #arguments.event.getValue('mc_adminToolInfo.myRights.ManagePaidRates')#;
	let manageFreeRates = #arguments.event.getValue('mc_adminToolInfo.myRights.ManageFreeRates')#;
	
	function removeMemberGroup(rateid,grpid,inc) {
		manageRates();
		var removeMGData = function(mg) {
			if (mg.success && mg.success.toLowerCase() == 'true'){
				reloadRatesTable();
			} else {
				alert('We were unable to remove this group from this rate.');
			}
		};
		var msg = 'Are you sure you want to remove this group from this rate?';
		if( confirm(msg)){
			var objParams = { rateid:rateid, groupid:grpid, include:inc, eventSRID:#this.siteResourceID# };
			TS_AJX('ADMINEVENT','deleteMemberGroup',objParams,removeMGData,removeMGData,10000,removeMGData);
		}
	}
	function moveRateGrouping(rgID,rowID,pRowID,dir) {
		manageRates();
		var moveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				moveRatesGridRow(rowID,pRowID,dir);
			}
			else {
				alert('We were unable to move this rate grouping.');
			}
		};
		var objParams = { rateGroupingID:rgID, registrationID:#local.strEvent.qryEventRegMeta.registrationID#, dir:dir };
		TS_AJX('ADMINEVENT','doRateGroupingMove',objParams,moveResult,moveResult,10000,moveResult);
	}
	function removeRateGrouping(rid,rgid) {
		manageRates();
		var removeRateData	= function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadRatesTable();
			} else {
				alert('We were unable to remove this rate grouping.');
			}
		};
		var msg = 'Are you sure you want to remove this rate grouping? Any rates in this rate grouping will be ungrouped.';
		if (confirm(msg)) {
			var objParams = { registrationID:rid, rateGroupingID:rgid, eventSRID:#this.siteResourceID# };
			TS_AJX('ADMINEVENT','deleteRateGrouping',objParams,removeRateData,removeRateData,10000,removeRateData);
		}
	}
	function moveRatesGridRow(rowID,pRowID,dir) {
		let movingRow, targetRow;

		if(dir == 'up'){
			movingRow = $('##evRatesTable ##'+rowID);
			targetRow = movingRow.closest('tr').prevAll('tr.child-of-'+pRowID+':first');
		}
		else {
			movingRow = $('##evRatesTable ##'+rowID).closest('tr').nextAll('tr.child-of-'+pRowID+':first'); /*next row will be moved to top*/
			targetRow = $('##evRatesTable ##'+rowID);
		}

		let movingRowID = movingRow.attr('id');
		movingRow.addClass('moveRow-' + movingRowID);
		markAssocRows(movingRowID,movingRowID);

		let arrMoveRows = $('##evRatesTable tr.moveRow-'+movingRowID);
		arrMoveRows.remove().insertBefore(targetRow);
		$('##evRatesTable tr.moveRow-'+movingRowID).removeClass('moveRow-' + movingRowID);
		resetRateRows(pRowID);
	}
	function markAssocRows(parentRowID,moveRowID) {
		let childRows = $('##evRatesTable tr.child-of-'+parentRowID);
		if (childRows.length) {
			childRows.addClass('moveRow-' + moveRowID).each(function() {
				markAssocRows($(this).attr('id'),moveRowID);
			});
		}
	}
	function resetRateRows(pRowID) {
		let childRows = $('##evRatesTable tr.child-of-'+pRowID).not('.default-nogrouping');
		if (childRows.length > 1) {
			childRows.find('a.rtRowMoveUp,a.rtRowMoveDown').removeClass('invisible');
			childRows.find('a.rtRowMoveUp').first().addClass('invisible');
			childRows.find('a.rtRowMoveDown').last().addClass('invisible');
		} else {
			childRows.find('a.rtRowMoveUp,a.rtRowMoveDown').addClass('invisible');
		}
	}
	function copyRatesFromOtherEventPrompt() {
		manageRates();
		let msg = '<div class="alert d-flex align-items-center pl-2 align-content-center alert-info fade show" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-solid fa-info-circle"></i></span><span><strong class="d-block">You are about to erase all current rates in this event and copy rates from another event.</strong> Your action will NOT affect existing registrants or revenue for this event.</span></div>';
	
		MCModalUtils.showModal({
			verticallycentered: true,
			size: 'lg',
			title: 'Confirmation Needed',
			strmodalbody: {
				content: msg
			},
			strmodalfooter : {
				classlist: 'text-right',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary',
				extrabuttonlabel: 'Choose Event',
				extrabuttononclickhandler: 'copyRatesFromOtherEvent'
			}
		});
	}
	function copyRatesFromOtherEvent(){
		MCModalUtils.hideModal();
		manageRates();
		$('##MCModal').on('hidden.bs.modal', function() {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Select Event to Copy Rates From',
				iframe: true,
				contenturl: '#this.link.linkEvent#&cid=#arguments.event.getValue("cid",0)#&eid=#arguments.event.getValue("eid",0)#&taskType=copyrates',
				strmodalfooter : {
					classlist: 'd-none'
				}
			});
		});
	}
	function addRateWindow(rid,glaid){
		manageRates();
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Add New Event Rate',
			iframe: true,
			contenturl: '#this.link.addRate#&eventID=#arguments.event.getValue('eID')#&registrationID=' + rid + (glaid ? '&glaid=' + glaid : ''),
			strmodalfooter: {
				classlist: 'text-right',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmRate :submit").click',
				extrabuttonlabel:'Save Rate',
			}
		});
	}
	function editRateWindow(rid){
		manageRates();
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Edit Event Rate',
			iframe: true,
			contenturl: '#this.link.editRate#&eventID=#arguments.event.getValue('eID')#&registrationID=#local.strEvent.qryEventRegMeta.registrationID#&rateID=' + rid,
			strmodalfooter: {
				classlist: 'text-right',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmRate :submit").click',
				extrabuttonlabel:'Save Rate',
			}
		});
	}
	function ratePermClose(){
		reloadRatesTable();
		MCModalUtils.hideModal();
	}
	function copyRate(ID) {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'md',
			title: 'Copy Rate',
			iframe: true,
			contenturl: '#this.link.copyRate#&registrationID=#local.strEvent.qryEventRegMeta.registrationID#&eid=#arguments.event.getValue("eid",0)#&rateID=' + ID,
			strmodalfooter: {
				classlist: 'd-flex',
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmCopyRate :submit").click',
				extrabuttonlabel: 'Save',
			}
		});
	}
	function removeRate(ID) {
		manageRates();
		var removeRateData	= function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadRatesTable();
			} else {
				alert('We were unable to remove this rate.');
			}
		};
		var msg = 'Are you sure you want to remove this rate?';
		if (confirm(msg)) {
			var objParams = { eventID:#arguments.event.getValue('eID')#, rateID:ID };
			TS_AJX('ADMINEVENT','deleteRate',objParams,removeRateData,removeRateData,10000,removeRateData);
		}
	}
	function manageRates() {
		toggleRateSelectionCheck(false);
		$('##divRateGroupingForm, ##divMassChangeForm, ##divRateAndAccOptionsForm').addClass('d-none');
	}
	function addRateGroupingWindow(){
		manageRates();
		$('##rateGroupingFormHeader').html('Add Rate Grouping');
		$('##divRateGroupingForm').removeClass('d-none');
		clearGroupingData();
	}
	function editRateGroupingWindow(rgid){
		manageRates();
		$.ajax({
			type : 'get',
			url : '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&pg=admin&mca_jsonlib=events&mca_jsonfunc=getRateGroupingData&rgID=' + rgid,
			dataType: 'json'
		}).success(function(r){
			if(r.SUCCESS == true){
				$('##rateGroupingFormHeader').html('Edit Rate Grouping');
				$('##rateGrouping').val(r.RATEGROUPING);
				$('##rateGroupingID').val(r.RATEGROUPINGID);
				$('##divRateGroupingForm').removeClass('d-none');
				$('html,body').animate({scrollTop: $('##divRateGroupingForm').position().top},500);
				$('##rateGrouping').focus();
			}
		}).error(function(err){
			alert(err);
		});
	}
	function saveRateGrouping() {
		var saveResult = function(r) {
			$('##btnSaveGrouping').attr('disabled',false);
			if (r.success && r.success.toLowerCase() == 'true'){
				clearGroupingData();
				manageRates();
				reloadRatesTable();
			} else {
				alert(r.errmsg && r.errmsg.length ? r.errmsg : 'We were unable to save this rate grouping. Try again.');
			}
		};
		if($('##rateGrouping').val().trim()!=''){
			$('##btnSaveGrouping').attr('disabled',true);
			var objParams = { rateGroupingID:$('##rateGroupingID').val(), rateGrouping:$('##rateGrouping').val(),
				registrationID:#local.strEvent.qryEventRegMeta.registrationID# };
			TS_AJX('ADMINEVENT','saveRateGrouping',objParams,saveResult,saveResult,10000,saveResult);
		} else {
			alert('Enter a name for this Rate Grouping.');
			return false;
		}
	}
	function clearGroupingData() {
		$('##rateGrouping').val('');
		$('##rateGroupingID').val(0);
	}
	function manageMassChangeRates() {
		manageRates();
		toggleRateSelectionCheck(true);
		$('##divMassChangeFormArea').html('<div><i class="fa-light fa-circle-notch fa-spin fa-2x"></i> Loading...</div>');
		$('##divMassChangeForm').removeClass('d-none');
		$('##divMassChangeFormArea').load('#this.link.manageMassRates#&registrationID=#local.strEvent.qryEventRegMeta.registrationID#&eid=#local.strEvent.qryEventMeta.eventID#', function() { mca_setupSelect2(); } );
	}
	function saveMassChangeRates() {
		var arrRateIDs = $('input[name="rateSelCheckbox"]:checked').map(function(){ return parseInt($(this).val()); }).get();
		if (arrRateIDs.length == 0) {
			alert("You have not selected any rates in the table.");
			return false;
		}
		
		var scheduleIDList = $('##eventScheduleIDs').val() || '';
		if(scheduleIDList.length) scheduleIDList = scheduleIDList.join(',');

		$('##btnSaveMassChange').attr('disabled',true);
		$('##spanSaveMassChangeArea').removeClass('d-none');

		var saveMassChangeRatesResult = function(r) {
			$('##btnSaveMassChange').attr('disabled',false);
			$('##spanSaveMassChangeArea').addClass('d-none');
			reloadRatesTable();
			if (r.success && r.success.toLowerCase() == 'true'){
				manageRates();
			} else {
				alert('There was an error applying the changes. Check the rate table and try again.');
			}
		};
		var objParams = { dtzid:#arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#, registrationID:#local.strEvent.qryEventRegMeta.registrationID#,
						  rateIDList:arrRateIDs.join(','), isHidden:$('##massisHidden').val(), rateGroupingID:$('##massrateGroupingID').val(),
						  rate:$('##massrate').val(), scheduleIDList:scheduleIDList, eventID:#arguments.event.getValue('eID')# };
		TS_AJX('ADMINEVENT','saveMassRates',objParams,saveMassChangeRatesResult,saveMassChangeRatesResult,20000,saveMassChangeRatesResult);
	}
	function manageRateAndAccOptions(){
		manageRates();
		$('##divRateAndAccOptionsForm').removeClass('d-none');
	}
	function validateEventRegRateAndAccDetailsForm() {
		var arrReq = new Array();
		mca_hideAlert(['err_ev_reg_rate_acc_detail']);

		<cfif local.strEvent.qryEventRegMeta.registrationtypeid EQ 1>
			if ($('##evGLAccountID').length && ($('##evGLAccountID').val() == 0 || $.trim($('##evGLAccountID').val()).length == 0)) arrReq[arrReq.length] = 'Choose the GL Account for this event.';
		</cfif>
		
		if (arrReq.length > 0) {
			mca_showAlert('err_ev_reg_rate_acc_detail', arrReq.join('<br/>'), true);
			return false;
		}
		
		$('.btnSaveEventRegRateAndAccDetails').attr('disabled', true);
		return true;
	}
	function initRatesTable(){
		evRatesTable = $('##evRatesTable').DataTable({
			"processing": true,
			"serverSide": true,
			"paging": false,
			"info": false,
			"language": {
				"emptyTable": "<div class='mt-2 alert alert-warning'>No rates defined. <a href='javascript:addRateWindow(#local.strEvent.qryEventRegMeta.registrationID#);' class='px-2'><i class='fa-regular fa-circle-plus'></i> Add Rate</a> or <a href='javascript:copyRatesFromOtherEventPrompt();' class='px-2'><i class='fa-regular fa-copy'></i> Copy Rates from Other Event</a></div>"
			},
			"ajax": { 
				"url": '#local.ratesListLink#',
				"type": "post",
				"data": function(d) { 
					if (window.reorderData && window.reorderData.length > 0) { 
						d.reorderData = JSON.stringify(window.reorderData); 
						window.reorderData = [];
					} 
					return d; 
				}
			},
			"autoWidth": false,
			"columns": [
				{
					"data": null,
					"render": function (data, type) {
						let renderData = '';
						if (type === 'display') {
							if (data.rowType == 'rate') {
								renderData += '<div class="row-drag-handle"><i class="fa-light fa-bars"></i></div>';
							}
						}
						return type === 'display' ? renderData : data;
					},
					"orderable": false
				},
				{ 
					"data": null,
					"render": function ( data, type, row, meta ) {
						return type === 'display' ? (data.rowType == 'rate' ? '<input type="checkbox" name="rateSelCheckbox" class="rateSelCheckbox" value="' + data.rateID + '">' : '') : data;
					},
					"width": "3%",
					"searchable": false,
					"orderable": false,
					"className": "align-top text-center rate-sel-col d-none"
				},
				{ 
					"data": null,
					"render": function ( data, type, row, meta ) {
						let thisRowID = data['DT_RowId'];
						let renderData = '';
						if (type === 'display')	{
							let cellPadding = data.level > 1 ? 24 * (data.level-1) : 0;
							let rowTypeIcon = '';

							if(data.rowType == 'rate') rowTypeIcon = '<i class="fas fa-money-bill text-goldenrod fa-fw pr-4"></i>';
							else if(data.rowType == 'group') rowTypeIcon = '<i class="fas fa-users text-green fa-fw pr-4"></i>';

							renderData += '<div style="padding-left:'+cellPadding+'px;">';
							if (data.hasChildren) {
								renderData += '<a href="javascript:toggleParentDisplay(\''+thisRowID+'\');" id="displayLabel_'+thisRowID+'"><i class="'+ (data.rowType == 'rateGroup' ? 'fas fa-folder-minus' : 'far fa-minus-square') +' fa-fw rowToggleBtn pr-2"></i> '+ rowTypeIcon + data.displayName+'</a>';
							} else {
								renderData += '<span><i class="fas fa-folder-tree fa-fw pr-2 invisible"></i> '+ rowTypeIcon + data.displayName+'</span>';
							}
							if(data.rowType == 'rate'){
								if(data.schedules.length) renderData += '<div class="ml-5 text-grey" title="'+data.schedulesWithDates+'">'+data.schedules+'</div>';
								else renderData += '<span class="float-right" title="No Date Ranges Assigned"><i class="fas fa-circle-exclamation text-danger fa-fw"></i></span>';
							}
							renderData += '</div>';
						}
						return type === 'display' ? renderData : data;
					},
					"width": "65%",
					"className": "align-top"
				},
				{ "data": null,
					"render": function ( data, type, row, meta ) {
						return type === 'display' ? (data.rowType == 'rate' ? data.rateFormatted : '') : data;
					},
					"width": "10%",
					"className": "text-right"
				},
				{ "data": null,
					"render": function ( data, type, row, meta ) {
						let thisRowID = data['DT_RowId'];
						let renderData = '';
						if (type === 'display') {
							switch(data.rowType) {
								case "rateGroup":
									renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-users"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1'+(data.rateGroupingID > 0 ? "" : " invisible")+'" onclick="editRateGroupingWindow('+data.rateGroupingID+');return false;" title="Edit Rate Grouping"><i class="fas fa-pencil"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-danger p-1 mx-1'+(data.rateGroupingID > 0 ? "" : " invisible")+'" onclick="removeRateGrouping(#arguments.event.getValue('registrationID')#,'+data.rateGroupingID+');return false;" title="Remove Rate Grouping"><i class="fas fa-trash-alt"></i></a>';
									renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-copy"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-green p-1 mx-1 rtRowMoveUp'+(data.rateGroupingID > 0 && data.canMoveUp ? "" : " invisible")+'" title="Move Rate Grouping Up" onclick="moveRateGrouping('+data.rateGroupingID+',\''+thisRowID+'\',\''+data.parentRowID+'\',\'up\');return false;"><i class="fas fa-arrow-up"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-green p-1 mx-1 rtRowMoveDown'+(data.rateGroupingID > 0 && data.canMoveDown ? "" : " invisible")+'" title="Move Rate Grouping Down" onclick="moveRateGrouping('+data.rateGroupingID+',\''+thisRowID+'\',\''+data.parentRowID+'\',\'down\');return false;"><i class="fas fa-arrow-down"></i></a>';
									break;
								case "rate":
									if((data.rate > 0 && managePaidRates == 1) || (data.rate == 0 && manageFreeRates == 1)){
										renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1" onclick="mca_showPermissions('+data.rateSRID+',null,null,null,null,null,\'Add Group for '+data.displayNameEncoded+'\',1,\'ratePermClose\',null,\''+data.childRateSRIDs+'\');return false;" title="Add Group to Rate"><i class="fa fa-users"></i></a>';
										renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1" onclick="editRateWindow('+data.rateID+');return false;" title="Edit Rate"><i class="fas fa-pencil"></i></a>';
										renderData += '<a href="##" class="btn btn-xs text-danger p-1 mx-1" onclick="removeRate('+data.rateID+');return false;" title="Remove Rate"><i class="fas fa-trash-alt"></i></a>';
										renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1" onclick="copyRate('+data.rateID+');return false;" title="Copy Rate"><i class="fas fa-copy"></i></a>';
										renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1 invisible"><i class="fas fa-arrow-up"></i></a>';
										renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1 invisible"><i class="fas fa-arrow-down"></i></a>';
									}
									break;
								case "group":
									renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-users"></i></a>';
									renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-pencil"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-danger p-1 mx-1" onclick="removeMemberGroup('+data.rateID+','+data.groupID+','+data.include+');return false;" title="Remove Group from Rate"><i class="fas fa-trash-alt"></i></a>';
									renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-copy"></i></a>';
									renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-arrow-up"></i></a>';
									renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-arrow-down"></i></a>';
									break;
							}
						}
						return type === 'display' ? renderData : data;
					},
					"className": "text-center align-top"
				}
			],
			"searching": false,
			"ordering": false,
			rowReorder: {
				dataSrc: "columnid",
				selector: '.row-drag-handle' 
			},
			createdRow: function (row, data, index) {
				$(row).attr('data-rowType', data['rowType']);
				$(row).attr('data-rateGroupingID', data['rateGroupingID']);
				if (data.rowType !== 'rate') {
					
					$(row).find('.row-drag-handle').css('pointer-events', 'none');
				}
			},
			drawCallback: function( row, data, dataIndex ) {
				toggleRateSelectionCheck(false);
				let rowsWithGroup = $('##evRatesTable tbody tr[data-rowType="group"]');
				let saveEVProgramRate = false;
				
				if (rowsWithGroup.length > 0 && !$('##isPriceBasedOnActual_1').is(':checked')) {
					$('##isPriceBasedOnActual_1').prop("checked", true);
					saveEVProgramRate = true;
				} else if(rowsWithGroup.length == 0 && $('##isPriceBasedOnActual_1').is(':checked')){
					$('##isPriceBasedOnActual_0').prop("checked", true);
					saveEVProgramRate = true;
				}
				if (saveEVProgramRate) {
					$('##frmEventRegRateAndAccDetails').submit();
				}
			}
		});
		evRatesTable.on('row-reorder', function (e, diff, edit) {
			let orderData = [];
			let isValidReorder = true;
			const $draggedTr = $(edit.triggerRow.node()); /* jQuery object of dragged row*/
			let $rateGroupTr = $draggedTr.prevAll('tr[data-rowtype="rateGroup"]').first(); /* Find nearest above*/

			let nearestRateGroupIndex = -1;
			if ($rateGroupTr.length) {
				nearestRateGroupIndex = evRatesTable.row($rateGroupTr).index(); /* Get DataTable index*/
			}
			diff = diff.filter(function(item) {
			return item.node.getAttribute("data-rowtype") === "rate";
			});
			diff.forEach(function (item) {
				let movedRow = evRatesTable.row(item.node).data();
				let oldIdx = item.oldPosition;
				let newIdx = item.newPosition;
		
				let newRow = evRatesTable.row(newIdx).data();

				
				if (movedRow.rateGroupingID !== newRow.rateGroupingID) {
					isValidReorder = false;
					return;
				}
					
					orderData.push({
					id: movedRow.rateID,
					rateGroupingID: movedRow.rateGroupingID,
					newOrder: item.newPosition - nearestRateGroupIndex
				});
				
			});

			if (isValidReorder) {
				window.reorderData = orderData;
			} else {
				window.reorderData = [];
				evRatesTable.draw(false);
				alert("Can only reorder rate rows within the same rate group.");
			}
		});
	}
	function toggleRateSelectionCheck(f){
		$('.rate-sel-col').toggleClass('d-none',!f);
	}
	function toggleParentDisplay(rowID) {
		let rowType = $('##evRatesTable ##'+rowID).attr('data-rowType');
		let rowToggleBtn = $('##evRatesTable ##displayLabel_'+rowID+' i.rowToggleBtn');
		rowToggleBtn.toggleClass(rowType == 'rateGroup' ? 'fa-folder-plus fa-folder-minus' : 'fa-plus-square fa-minus-square');
		let showChildren = rowToggleBtn.hasClass(rowType == 'rateGroup' ? 'fa-folder-minus' : 'fa-minus-square');
		toggleChildRows(rowID,showChildren);
	}
	function toggleChildRows(rowID,f) {
		$('##evRatesTable tr.child-of-'+rowID).toggleClass('d-none',!f).each(function(i,thisRow) {
			let expandedIconClass = $(this).attr('data-rowType') == 'rateGroup' ? 'fa-folder-minus' : 'fa-minus-square';
			if ($(this).find('i.rowToggleBtn').hasClass(expandedIconClass)) toggleChildRows($(this).attr('id'),f);
		});
	}
	function reloadRatesTable() {
		evRatesTable.draw();
	}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.ratesJS#">

<!--- button bar --->
<div class="toolButtonBar">
	<cfif arguments.event.getValue('mc_admintoolInfo.myrights.manageFreeRates') + arguments.event.getValue('mc_admintoolInfo.myrights.managePaidRates') GT 0>
	<cfoutput>
		<div><a href="javascript:manageRates();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to manage the rate information."><i class="fa-regular fa-money-check-dollar-pen"></i> Manage Rates</a></div>
		<div><a href="javascript:addRateGroupingWindow();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add rate grouping."><i class="fa-regular fa-users-medical"></i> Add Rate Grouping</a></div>
		<div><a href="javascript:addRateWindow(#local.strEvent.qryEventRegMeta.registrationID#);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add rate."><i class="fa-regular fa-circle-plus"></i> Add Rate</a></div>
		<div><a href="javascript:manageRateAndAccOptions();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to manage accounting & rate options."><i class="fa-regular fa-coins"></i> Accounting & Rate Options</a></div>
		<div><a href="javascript:copyRatesFromOtherEventPrompt();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to copy rates from other event."><i class="fa-regular fa-copy"></i> Copy Rates from Other Event</a></div>
		<div><a href="javascript:manageMassChangeRates();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to mass change of rates."><i class="fa-regular fa-money-bill-transfer"></i> Mass Change Rates</a></div>
	</cfoutput>
	</cfif>
</div>

<div id="divRateGroupingForm" class="mb-4 d-none">
	<form name="frmGrouping" id="frmGrouping" method="post" onSubmit="saveRateGrouping(); return false;">
		<input type="hidden" name="rateGroupingID" id="rateGroupingID" value="0">
		<div class="card card-box mt-2">
			<div class="card-header bg-light">
				<div id="rateGroupingFormHeader" class="card-header--title font-weight-bold font-size-md">Add Rate Grouping</div>
			</div>
			<div class="card-body p-3">
				<div class="form-group row">
					<label for="rateGrouping" class="col-sm-auto col-form-label-sm font-size-md">Rate Grouping</label>
					<div class="col-sm">
						<input type="text" name="rateGrouping" id="rateGrouping" autocomplete="off" value="" class="form-control form-control-sm" maxlength="200" >
					</div>
					<div class="col-sm-auto pl-sm-0">
						<button class="btn btn-sm btn-primary" type="button" id="btnSaveGrouping" onclick="saveRateGrouping();">Save</button>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>

<div id="divMassChangeForm" class="mb-4 d-none">
	<div class="card card-box mt-2">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Mass Change Rates, Dates, Groupings, and Availability</div>
		</div>
		<div class="card-body p-3">
			<div id="divMassChangeFormArea" class="mt-3 mb-4"></div>
		</div>
	</div>
</div>

<div id="divRateAndAccOptionsForm" class="mb-4 d-none">
	<cfoutput>
	<div id="err_ev_reg_rate_acc_detail" class="alert alert-danger my-2 d-none"></div>
	<form name="frmEventRegRateAndAccDetails" id="frmEventRegRateAndAccDetails" action="#this.link.saveEventRegRateAndAccDetails#" method="POST" onsubmit="return validateEventRegRateAndAccDetailsForm();">
		<input type="hidden" name="eID" value="#arguments.event.getValue('eID')#">
		<input type="hidden" name="registrationid" value="#arguments.event.getValue('registrationid')#">
		<input type="hidden" name="registrationTypeid" value="#arguments.event.getValue('registrationTypeid')#">
		<div class="form-row mt-3">
			<div class="col-xl-6 col-lg-12 mb-1">
				<div class="card card-box card-transparent h-100">
					<div class="card-header bg-light">
						<div class="card-header--title font-weight-bold font-size-md">Rate Options</div>
					</div>
					<div class="card-body">
						<div class="mb-2">Rate Selection *</div>
						<div class="ml-3">
							<div class="form-check">
								<input type="radio" name="isPriceBasedOnActual" id="isPriceBasedOnActual_1" value="1" class="form-check-input" <cfif arguments.event.getValue('isPriceBasedOnActual',0) is 1>checked</cfif>>
								<label class="form-check-label" for="isPriceBasedOnActual_1">Rate selection is limited to qualifying group memberships</label>
							</div>
							<div class="form-check">
								<input type="radio" name="isPriceBasedOnActual" id="isPriceBasedOnActual_0" value="0" class="form-check-input" <cfif arguments.event.getValue('isPriceBasedOnActual',0) is 0>checked</cfif>>
								<label class="form-check-label" for="isPriceBasedOnActual_0">Rate is based on registrant's selection of all possible rates</label>
							</div>
						</div>
						<div class="my-2">Bulk Rate Selection *</div>
						<div class="ml-3">
							<div class="form-check">
								<input type="radio" name="bulkCountByRate" id="bulkCountByRate_0" value="0" class="form-check-input" <cfif arguments.event.getValue('bulkCountByRate',0) is 0>checked</cfif>>
								<label class="form-check-label" for="bulkCountByRate_0">Bulk rate quantity applies to all registrants regardless of rate</label>
							</div>
							<div class="form-check">
								<input type="radio" name="bulkCountByRate" id="bulkCountByRate_1" value="1" class="form-check-input" <cfif arguments.event.getValue('bulkCountByRate',0) is 1>checked</cfif>>
								<label class="form-check-label" for="bulkCountByRate_1">Bulk rate quantity only applies to registrants with the same rate</label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xl-6 col-lg-12 mb-1">
				<div class="card card-box card-transparent h-100">
					<div class="card-header bg-light">
						<div class="card-header--title font-weight-bold font-size-md">Accounting Options</div>
					</div>
					<div class="card-body">
						<!--- payment profiles --->
						#createObject("component","model.admin.common.modules.paymentProfileSelector.paymentProfileSelector").getPaymentProfileSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="evRegMPProfiles", selectedPaymentProfileIDList=valueList(local.qryCurrentProfiles.profileid), fieldLabel="Supported Payment Methods *", colCount=2, allowOffline=false, allowPayLater=true).html#
						#local.strRevenueGLAcctWidget.html#
					</div>
				</div>
			</div>
		</div>
		<div class="mt-2">
			<button type="submit" name="btnSaveEventRegRateAndAccDetails" class="btn btn-sm btn-primary btnSaveEventRegRateAndAccDetails">Save Details</button>
		</div>
		<hr>
	</form>
	</cfoutput>
</div>

<h5>Rates</h5>
<div class="mb-2">Rates must be defined to accept registrants for this event. Free registrations can be added as $0 rates.</div>
<table id="evRatesTable" class="table table-sm table-bordered table-hover" style="width:100%;">
	<thead>
		<tr>
			<th id="columnid"></th>
			<th class="rate-sel-col d-none"></th>
			<th>Rate Name</th>
			<th>Amount</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>