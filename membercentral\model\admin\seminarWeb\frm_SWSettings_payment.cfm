<cfsavecontent variable="local.paymentJS">
	<cfoutput>
	#local.strRevenueGLAcctWidget.js#
	<script language="JavaScript">
		function saveSWSettingPayment(callback) {
			mca_hideAlert('err_payment');
			var arrReq = [];
			var am = $('input[name="handlesOwnPayment"]:checked').val();
			if (am == 1) {
				if (! $('input[name="swRegMPProfiles"]:checked').length) arrReq.push('Select at least one supported payment method.');
				if ($('##revenueGLAccountID').length && ($.trim($('##revenueGLAccountID').val()).length == 0 || $('##revenueGLAccountID').val() == 0)) arrReq.push('Select a Revenue GL Account.');
			}
			if (arrReq.length) {
				$('##err_payment').html(arrReq.join('<br/>')).removeClass('d-none');
				$('html,body').animate({scrollTop: $('##err_payment').offset().top-120},500);
				$('##program-payment .card-footer .save-button').prop('disabled',false);
			}
			else {
				var saveResult = function(r) {
					$('##frmSWSettingPayment,##divSWSettingpaymentSaveLoading').toggle();	
					if (r.success && r.success.toLowerCase() == 'true') {
						if(!$("##program-payment .card-header:first ##saveResponse").length)
							$("##program-payment .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
							$('##program-payment .card-header:first ##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(5000);
						
						$('##program-payment .card-footer .save-button').prop('disabled',false);
						if (callback) {
							callback();
						}	
					} else {
						var arrReq = [];
						arrReq.push(r.err && r.err.length ? r.err : 'We were unable to save payment settings.');
						$('##err_payment').html(arrReq.join('<br/>')).removeClass('d-none');
						$('html,body').animate({scrollTop: $('##err_payment').offset().top-120},500);
						$('##program-payment .card-footer .save-button').prop('disabled',false);
					}
				}

				var revGL = 0;
				if ($.trim($('##revenueGLAccountID').val()).length)
					revGL = $('##revenueGLAccountID').val();
				$('##frmSWSettingPayment,##divSWSettingpaymentSaveLoading').toggle();

				let swRegMPProfiles = $('input[name="swRegMPProfiles"]:checked').map(function() { return this.value; }).get().join(',');

				var objParams = { handlesOwnPayment:$('input[name="handlesOwnPayment"]:checked').val(), revenueGLAccountID:revGL, swRegMPProfiles:swRegMPProfiles};
				TS_AJX('SEMWEBPARTICIPANTS','updateSWSettingsPayment',objParams,saveResult,saveResult,20000,saveResult);
			}
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.paymentJS#">
<cfoutput>

<div id="err_payment" class="alert alert-danger mb-2 mt-2 d-none"></div>
<form name="frmSWSettingPayment" id="frmSWSettingPayment">
	<div class="form-group row">
		<div class="col-sm-3">Accounting Mode</div>
		<div class="col-sm-9">
			<div class="form-check">
				<input type="radio" name="handlesOwnPayment" id="handlesOwnPayment_0" value="0" class="form-check-input" onclick="showOrHideSWPmtSettings();" <cfif local.qryAssociation.handlesOwnPayment is 0>checked</cfif>>
				<label for="handlesOwnPayment_0" class="form-check-label">SeminarWeb receives registration payment.</label>
			</div>
			<div class="form-check">
				<input type="radio" name="handlesOwnPayment" id="handlesOwnPayment_1" value="1" class="form-check-input" onclick="showOrHideSWPmtSettings();" <cfif local.qryAssociation.handlesOwnPayment is 1>checked</cfif>>
				<label for="handlesOwnPayment_1" class="form-check-label">Association receives registration payment and records transactions.</label>
			</div>
		</div>
	</div>
	<div id="divPayMethods" <cfif local.qryAssociation.handlesOwnPayment is 0> class="d-none"</cfif>>
		<div class="form-group row mt-2">
			<div class="col">
				#createObject("component","model.admin.common.modules.paymentProfileSelector.paymentProfileSelector").getPaymentProfileSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="swRegMPProfiles", selectedPaymentProfileIDList=valueList(local.qryCurrentProfiles.profileid), fieldLabel="Supported Payment Methods *", colCount=2, allowOffline=false, allowPayLater=true).html#
			</div>
		</div>
		<div class="form-group">
			#local.strRevenueGLAcctWidget.html#
		</div>
	</div>
</form>
<div id="divSWSettingpaymentSaveLoading" style="display:none;">
	<div class="text-center">
		<br/>
		<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
		<br/><br/>
		<b>Saving payment settings...</b>
		<br/>
	</div>
</div>
</cfoutput>