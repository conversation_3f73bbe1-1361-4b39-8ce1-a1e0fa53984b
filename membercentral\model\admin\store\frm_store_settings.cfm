<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<cfif structKeyExists(local, "strProductRevenueGLAcctWidget") and structKeyExists(local, "strShippingRevenueGLAcctWidget")>
		#local.strProductRevenueGLAcctWidget.js#
		#local.strShippingRevenueGLAcctWidget.js#
	</cfif>
	<script>
	<cfif this.storeInfo.OfferStreams>
		var streamProfilesTable ;
		function initStreamProfilesTable() {
			streamProfilesTable = $('##streamProfilesTable').DataTable({
				"processing": true,
				"serverSide": true,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.streamProfilesLink#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display') {
									renderData += '<div>'+data.profileName+'</div>';
									renderData += '<div class="text-dim font-size-sm">' + data.providerName + '</div>';
								}
								return type === 'display' ? renderData : data;
							},
							"className": "align-top",
							"orderable": true
					},
					{ "data": "isActive", "className": "text-center align-top", "width": "10%" },
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 m-1'+ (data.canEdit ? '' : ' invisible') +'" '+ (data.canEdit ? 'onclick="editStreamProfile('+data.profileID+');return false;"' : '') +' title="Edit '+ data.profileNameEnc +'"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-danger p-1 m-1'+ (data.canDelete ? '' : ' invisible') +'" '+ (data.canDelete ? 'onclick="removeStreamProfile('+data.profileID+');return false;"' : '') +' id="btnDelSP'+data.profileID+'" title="Delete '+ data.profileNameEnc +'" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "15%",
						"className": "text-center align-top",
						"orderable": false
					}
				],
				"order": [[0, 'asc']],
				"searching": true
			});
		}
		function removeStreamProfile(pID){
			var removeData = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					streamProfilesTable.draw(false);
				} else {
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					mca_showAlert('err_streamProfile', 'We were unable to remove this stream profile.', true);
				}
			};
			let delBtn = $('##btnDelSP'+pID);
			mca_initConfirmButton(delBtn, function(){
				var objParams = { pid:pID, storeSRID:#this.siteResourceID# };
				TS_AJX('ADMINSTORE','deleteStreamProfile',objParams,removeData,removeData,10000,removeData);
			});
		}
		function editStreamProfile(pID) {
			var providerID=0;
			if(pID == 0){
				providerID = $('##providerID').val();
				if(providerID == '' || providerID == 'null'){
					mca_showAlert('err_streamProfile', 'Please select Stream Provider.', false);
					return false;
				} else {
					mca_hideAlert('err_streamProfile');
				}
			}
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				title: (pID == 0 ?'Add':'Edit') + ' Stream Profile',
				iframe: true,
				contenturl: '#this.link.editStreamProfile#&storeID=#arguments.event.getTrimValue("storeID")#&profileID=' + pID + (pID==0?'&providerID='+providerID:''),
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'doSaveStreamProfile',
					extrabuttonlabel: 'Save Stream Profile'
				}
			});
		}
		function doSaveStreamProfile() {
			$('##MCModalBodyIframe')[0].contentWindow.validateStreamProfileAndSave();
		}
	</cfif>
		function validateStoreSettings() {
			mca_hideAlert(['err_storesettings','err_emailrecipient']);
			var arrReq = new Array();
			var emailRegEx = new RegExp("#application.regEx.email#","gi");
			var emailReplyTo = $('##emailReplyTo').val();

			if ($.trim($('##emailRecipient').val()).length == 0) arrReq[arrReq.length] = 'E-mail Orders To is required.';
			if ($.trim($('##emailReplyTo').val()).length == 0 || !(emailRegEx.test(emailReplyTo))) {
				 arrReq[arrReq.length] = 'Please enter valid Order Notification Reply-To.';
			}
			if ($.trim($('##maxRecords').val()).length == 0){ 
				arrReq[arrReq.length] = 'Product Display is required';
			}else if((parseInt($('##maxRecords').val())>0)==false){
				arrReq[arrReq.length] = 'Enter a valid Product Display.';			
			};
			if (! $('input[name="merchantProfiles"]:checked').length) arrReq[arrReq.length] = 'Payment Methods is required.';
			
			if (arrReq.length > 0) {
				mca_showAlert('err_storesettings', arrReq.join('<br/>'), true);
				$("button##saveBtn").prop('disabled',false);
				return false;
			} else {
				$("button##saveBtn").prop('disabled',true);
				return true;
			}
		}
		$(function() {
			mca_setupSelect2();
			mca_setupTagsInput('emailRecipient', 'err_emailrecipient', "#application.regEx.email#", 'email address');
		});		
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gridJS)#">

<cfoutput>
<div>
	<form action="#local.formLink#" method="POST" name="frmStore" onsubmit="return validateStoreSettings();">
		<input type="hidden" name="storeApplicationInstanceID" value="#arguments.event.getTrimValue('storeApplicationInstanceID')#">
		<input type="hidden" name="mainContentID" value="#this.storeInfo.MainContentID#">
		<input type="hidden" name="orderReceiptFooterContentID" value="#this.storeInfo.orderReceiptFooterContentID#">
		<div id="err_storesettings" class="alert alert-danger mb-2 mt-2 d-none"></div>
		<div id="err_emailrecipient" class="alert alert-danger mb-2 d-none"></div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="emailRecipient" id="emailRecipient" value="#this.storeinfo.emailRecipient#" class="form-control">
					<label for="emailRecipient">E-mail Orders To *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="emailReplyTo" id="emailReplyTo" value="#this.storeinfo.emailReplyTo#" class="form-control" maxlength="200">
					<label for="emailReplyTo">Order Notification Reply-To *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="maxRecords" id="maxRecords" value="#this.storeinfo.MaxRecords#" class="form-control">					
					<label for="maxRecords">## Products to Display Per Page *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<select name="showProductID" id="showProductID"  class="form-control">
						<option value="0" <cfif this.storeinfo.showProductID eq "0">selected</cfif>>No</option>
						<option value="1" <cfif this.storeinfo.showProductID eq "1">selected</cfif>>Yes</option>
					</select>
					<label for="showProductID">Show Product ID *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<select name="showCartThumbnails" id="showCartThumbnails" class="form-control">
						<option value="0" <cfif this.storeinfo.showCartThumbnails eq "0">selected</cfif>>No</option>
						<option value="1" <cfif this.storeinfo.showCartThumbnails eq "1">selected</cfif>>Yes</option>
					</select>
					<label for="showCartThumbnails">View Thumbnails in Cart *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<select name="OfferAffirmations" id="OfferAffirmations" class="form-control">
						<option value="0" <cfif this.storeinfo.OfferAffirmations eq "0">selected</cfif>>No</option>
						<option value="1" <cfif this.storeinfo.OfferAffirmations eq "1">selected</cfif>>Yes</option>
					</select>
					<label for="OfferAffirmations">Offer Affirmations *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<select name="OfferStreams" id="OfferStreams" class="form-control">
						<option value="0" <cfif this.storeinfo.OfferStreams eq "0">selected</cfif>>No</option>
						<option value="1" <cfif this.storeinfo.OfferStreams eq "1">selected</cfif>>Yes</option>
					</select>
					<label for="OfferStreams">Offer Streams *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<select name="DisplayCredit" id="DisplayCredit" class="form-control">
						<option value="0" <cfif this.storeinfo.displayCredit eq "0">selected</cfif>>No</option>
						<option value="1" <cfif this.storeinfo.displayCredit eq "1">selected</cfif>>Yes</option>
					</select>
					<label for="DisplayCredit">Display credit on product *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<select name="DefaultSortOrder" id="DefaultSortOrder" class="form-control">
						<option value="prodTitle" <cfif this.storeinfo.defaultSortOrder eq "prodTitle">selected</cfif>>Product Title</option>
						<option value="prodDateAsc" <cfif this.storeinfo.defaultSortOrder eq "prodDateAsc">selected</cfif>>Product Date Ascending</option>
						<option value="prodDateDesc" <cfif this.storeinfo.defaultSortOrder eq "prodDateDesc">selected</cfif>>Product Date Descending</option>
					</select>
					<label for="DefaultSortOrder">Default Sort Order *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col-sm-12">
				#local.strProductRevenueGLAcctWidget.html#
			</div>
		</div>
		<div class="form-row mb-3">
			<div class="col-sm-12">
				#local.strShippingRevenueGLAcctWidget.html#
			</div>
		</div>

		<div class="form-group mb-4">
			<div class="small">Organization Identity used for Support Contact Information</div>
			#local.strOrgIdentitySelector.html#	
		</div>

		#createObject("component","model.admin.common.modules.paymentProfileSelector.paymentProfileSelector").getPaymentProfileSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="merchantProfiles", selectedPaymentProfileIDList=local.selectedMPList, fieldLabel="Supported Payment Methods", allowOffline=false, allowPayLater=true).html#

		<div class="form-row">
			<div class="col">
				#application.objWebEditor.showContentBoxWithLinks(fieldname='mainContent', fieldlabel='Content that appears on the store homepage', contentID=this.storeinfo.mainContentID, content=this.storeinfo.mainContent, allowMergeCodes=0,supportsBootstrap=true, allowVersioning=true)#
			</div>
		</div>
		<div class="form-label-group my-2">
			<div class="input-group">
				<textarea name="orderReceiptFooterContent" id="orderReceiptFooterContent" class="form-control" rows="5">#this.storeinfo.orderReceiptFooterContent#</textarea>
				<label for="orderReceiptFooterContent">Content that appears on the store order purchase confirmation mail footer *</label>
			</div>
			<div class="text-right font-size-md mt-1">No HTML tags allowed.</div>
		</div>
		<cfif this.storeInfo.OfferStreams>
			<div class="card card-box mt-4">
				<div class="card-header bg-light">
					<div class="card-header--title font-weight-bold font-size-md">Stream Profiles</div>
				</div>
				<div class="card-body">
					<div id="err_streamProfile" class="alert alert-danger d-none"></div>
					<div class="form-label-group">
						<div class="input-group">
							<select id="providerID" name="providerID" class="form-control input-group-item-select2-prepend" data-toggle="custom-select2">
								<option value=""></option>
								<cfloop query="local.qryStreamProviders">
									<option value="#local.qryStreamProviders.providerID#">#local.qryStreamProviders.providerName#</option>
								</cfloop>
							</select>
							<label for="providerID">Stream Provider</label>
							<div class="input-group-append">
								<button type="button" name="btnAddStreamProfile" class="btn input-group-text" onclick="editStreamProfile(0);">Add Stream Profile</button>
							</div>
						</div>
					</div>
					
					<table id="streamProfilesTable" class="table table-sm table-striped table-bordered" style="width:100%">
						<thead>
							<tr>
								<th>Stream Profile</th>
								<th>Active</th>
								<th>Actions</th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
		</cfif>
		
		<div class="form-row mt-3">
			<div class="col">
				<div class="form-group">
					<div class="form-label-group">
						<div class="input-group input-group">
							<input type="text" name="justview1" id="justview1" value="/?#local.baseTestLink#" class="form-control" readonly="readonly" onclick="this.select();"/>
							<div class="input-group-append">
								<span class="input-group-text"><a target="_blank" href="/?#local.baseTestLink#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
							</div>
							<label for="justview1">Internal Product URL</label>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-group">
					<div class="form-label-group">
						<div class="input-group input-group">
							<input type="text" name="justview2" id="justview2" value="#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?#local.baseTestLink#" class="form-control" readonly="readonly" onclick="this.select();"/>
							<div class="input-group-append">
								<span class="input-group-text"><a target="_blank" href="#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?#local.baseTestLink#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
							</div>
							<label for="justview2">External Product URL</label>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="form-group">
			<div class="text-right">
				<button id="saveBtn" name="btnSave" type="submit" class="btn btn-sm btn-primary">Save Store Settings</button>
			</div>
		</div>
	</form>
</div>
</cfoutput>
