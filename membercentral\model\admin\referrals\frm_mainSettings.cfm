<cfparam name="local.msgText" default="" />
<cfsavecontent variable="local.feeStructureRow">
	<cfoutput>
	<tr id="0">
		<td align="center">
			<input name="cumulativeChargedAmount"  id="cumulativeChargedAmount_0" required="false" type="text" size="10" maxlength="10" value="0" onChange="setFeeStructureChange();" class="form-control form-control-sm"/>
		</td>
		<td align="center">
			<input name="cumulativeFeePercent"  id="cumulativeFeePercent_0" required="false" type="text" size="10" maxlength="10" value="0" onChange="setFeeStructureChange();" class="form-control form-control-sm"/>
		</td>
		<cfif NOT local.isFeeStructureTotals>
			<td align="center">
				<input name="rangeInitialChargeFeePercent"  id="rangeInitialChargeFeePercent_0" required="false" type="text" size="10" maxlength="10" value="0" onChange="setFeeStructureChange();" class="form-control form-control-sm"/>
			</td>
		<cfelse>
			<input name="rangeInitialChargeFeePercent"  id="rangeInitialChargeFeePercent_0" type="hidden" value="0" />
		</cfif>
		<td align="center"><a href="####" onclick="removeFeeStructureRow();" title="Remove this fee structure level.">Remove</a></td>
	</tr>	
	</cfoutput>
</cfsavecontent>
<cfsavecontent variable="local.setttingsJS">
	<cfoutput>
	<style type="text/css">
		.select2-container .select2-search--inline input{width: 0px!important;}	
		.select2-container.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered .select2-selection__clear{right: 0px!important;}	
	</style>
	<cfif structKeyExists(local, "strReferralGLAcctWidget") AND structKeyExists(local, "strClientGLAcctWidget") AND structKeyExists(local, "strFEGLAcctWidget")>
		#local.strReferralGLAcctWidget.js#
		#local.strClientGLAcctWidget.js#
		#local.strFEGLAcctWidget.js#
	</cfif>
	<script language="JavaScript">
		let feeTypeTable;
		var gridInitArray = new Array();
		gridInitArray["referralCenter"] = false;
		gridInitArray["emailTemplates"] = false;
		var #toScript(this.link.previewReferralSMS,"previewSMSLink")#

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			let panelTable;
			
			function initializePanelTable() {
				panelTable = $('##panelTable').DataTable({
					"paging": false,
					"info": false,
					"ajax": "#local.panelLink#",
					"autoWidth": false,
					"columnDefs": [{
						"targets": 0,
						"searchable": true,
						"visible": false
					}],
					"columns": [
						{ "data": "name"},
						{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display') {
									var shortDesc = "";
									var toggleIcon = "";
									if($.trim(data.shortDesc).length != 0){
										shortDesc = ' (' + data.shortDesc + ')';
									}
									if(data.subPanelCount !=0){
										toggleIcon = '<i class="fa fa-arrow-right mr-1" aria-hidden="true"></i>';
									}
									if(data.panelParentID != null){
										renderData += '<span class="ml-5 child" panelid='+data.panelID+' parentid='+data.panelParentID+'>'+data.name + shortDesc +'</span>';
									}else{
										renderData += '<span class="parent" panelid='+data.panelID+' subPanelCount='+data.subPanelCount+' isdelete='+data.isdelete+'>'+ toggleIcon + data.name + shortDesc + '</span>';
									}
								}
								return type === 'display' ? renderData : data;
							},
							"searching": false,
							"orderable": false
						},
						{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display') {
									if(data.isdelete == 1){
										renderData += '<a href="javascript:deletePanel('+data.panelID+');" id="btnDel'+data.panelID+'" class="btn btn-sm text-danger p-1" title="Delete Panel"><i class="fa-solid fa-trash-can"></i></a>';
									}else{
										renderData += '<a class="btn btn-sm p-1 m-1 invisible"><i class="fa-solid fa-trash-can"></i></a>';
									}
								}
								return type === 'display' ? renderData : data;
							},
							"width": "15%",
							"className": "align-top text-center",
							"orderable": false
						}
					],
					"order": [],
					"searching": true,
					"createdRow": function( row, data, index ) {
						if (data.statusName == 'Inactive'){
							$(row).addClass('table-warning');
						}else if(data.statusName == 'Deleted'){
							$(row).addClass('table-danger');
						}
					},
					"drawCallback": function( settings ) {
						$(".parent").each(function(){
							$(".child[parentid='"+$(this).attr("panelid")+"']").parents('tr').hide();
						});
						$(".parent").parent().click(function(){
							if($(".child[parentid='"+$("span",this).attr("panelid")+"']").is(":visible")){
								$(".child[parentid='"+$("span",this).attr("panelid")+"']").parents('tr').hide();
								$(this).find("i").removeClass("fa-arrow-down").addClass("fa-arrow-right");
							}else{
								$(".child[parentid='"+$("span",this).attr("panelid")+"']").parents('tr').show();
								$(this).find("i").removeClass("fa-arrow-right").addClass("fa-arrow-down");
							}                        
						});
						$('##panelTable_filter input').on('input', function() {
							if($.trim($(this).val()).length){
								$(".child").parents('tr').show();
								$(".parent").find("i").removeClass("fa-arrow-right").addClass("fa-arrow-down");
							}else{
								$(".parent").each(function(){
									$(".child[parentid='"+$(this).attr("panelid")+"']").parents('tr').hide();
									$(this).find("i").removeClass("fa-arrow-down").addClass("fa-arrow-right");
								});
							}                        
						});
					}
				});
			}
			function purgePanels(){
				var content = '
					<table id="panelTable" class="table table-sm table-striped table-bordered" style="width:100%">
						<thead>
							<tr>
								<th></th>
								<th>Panels / Sub-Panels</th>
								<th>Actions</th>
							</tr>
						</thead>
					</table>';
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					title: 'Purge Panels',
					strmodalbody: { content: content },
					strmodalfooter : { showclose: false }
				});
				
				initializePanelTable();
			}
			function deletePanel(pid) {
				var removeItem = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						var parentid = 0;
						if($(".child[panelid="+pid+"]").length){
							parentid = $(".child[panelid="+pid+"]").attr("parentid");
						}
						$('##panelRow_'+pid).remove();
						$(".parent[panelid="+parentid+"]").attr("subpanelcount",$(".child[parentid="+parentid+"]").length);
						$(".parent[subpanelcount=0][isdelete=0]").parents('tr').remove();
					} else {
						delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
						alert(r.msg ? r.msg : 'We were unable to delete panel. Try again.');
					}
				};
				let delElement = $('##btnDel'+pid);
				mca_initConfirmButton(delElement, function(){
					var objParams = { panelID:pid, referralID:#this.referralID#};
					TS_AJX('ADMINREFERRALS','doRemovePanel',objParams,removeItem,removeItem,10000,removeItem);
				});
			}
		</cfif>

		function initReferralFeeTypesTable() {
			feeTypeTable = $('##feeTypeTable').DataTable({
				"processing": true,
				"serverSide": true,
				"paging": false,
				"scrollY": "350px",
				"scrollCollapse": true,
				"language": {
					"emptyTable": "No Referral Fee Types Found."
				},
				"ajax": { 
					"url": "#local.feeTypeLink#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ "data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div>';
								if(data.isDefault) {
									renderData +=  '<span class="float-right"><span class="badge badge-info ml-1">Default</span></span>';
								}
								renderData += '<div>' + data.feeTypeName + (data.isActive == 0 ? ' <span class="badge badge-warning ml-1">Inactive</span>' : '') + '</div>';
								renderData += '</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top"
					},
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="##" class="btn btn-sm text-primary p-1 mr-1" title="Edit Fee Type" onclick="editFeeType('+data.feeTypeID+');return false;"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 m-1" title="'+(data.isActive ? 'Deactivate' : 'Activate')+' Type" onclick="toggleFeeTypeStatus('+data.feeTypeID+');return false;"><i class="fa-solid fa-shuffle"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "text-center align-top",
						"width": "20%",
						"orderable": false
					}
				],
				"searching": false,
				"ordering": [[0,'asc']]
			});
		}
		function reloadFeeTypesTable() { feeTypeTable.draw();}
		function editFeeType(fID) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: fID > 0 ? 'Edit Fee Type' : 'Add Fee Type',
				iframe: true,
				contenturl: '#this.link.editFeeType#&typeID=' + fID,
				strmodalfooter : {
					classlist: 'd-flex',
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSubmit").click',
					extrabuttonlabel: 'Save',
				}
			});
		}
		function toggleFeeTypeStatus(fID) {
			var toggleResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') { reloadFeeTypesTable(); }
				else {
					alert('We ran into an error while toggling this Fee Type. Try again.');
				}
			};
			var objParams = { typeID:fID };
			TS_AJX('ADMINREFERRALS','toggleFeeTypeStatus',objParams,toggleResult,toggleResult,10000,toggleResult);
		}
		function validateSettingsFrm(){
			var errorMsg = "";
	
			if( ($.trim($('##title').val()).length == 0))
				errorMsg += '- Enter the title. <br>';
			
			if( $('##allowPanelMgmt').is(':checked') && (isNaN($('##maxNumOfPanel').val()) || $.trim($('##maxNumOfPanel').val()) == '' || $('##maxNumOfPanel').val() <= 0 ))
				errorMsg += '- Maximum number of Panels should be greater than 0. <br>';

			if( $('##feMaxMemberRefNum').length && (isNaN($('##feMaxMemberRefNum').val()) || $.trim($('##feMaxMemberRefNum').val()) == '' || $('##feMaxMemberRefNum').val() <= 0 ))
				errorMsg += '- Maximum number of Member Referrals in Front-End should be greater than 0. <br>'; 
			
			if($('.displayContactGroupList').length){
				var _checkedReq = false;
				$('.displayContactGroupList').each(function(){
					if($(this).is(':checked'))
						_checkedReq = true;
				});
				if(!_checkedReq){
					errorMsg += '- At least ONE of the following fields must be set to display on front end. <br> E-mail<br> Home Phone##<br> Cell Phone ##<br> Alternate Phone ##<br>'; 
				}
			}
			if($('.requiredContactGroupList').length){
				var _checkedReq = false;
				$('.requiredContactGroupList').each(function(){
					if($(this).is(':checked')){
						_checkedReq = true;
					}
				});
				if(!_checkedReq){
					errorMsg += '- At least ONE of the following fields must be set to required. <br> E-mail<br> Home Phone##<br> Cell Phone ##<br> Alternate Phone ##<br>'; 
				}
			}

			if($('.clFeDspLimitNumOfReferrals').length){
				if( $('##feDspLimitNumOfReferrals').is(':checked') && (isNaN($('##feMaxRefPerClient').val()) || $.trim($('##feMaxRefPerClient').val()) == '' || $('##feMaxRefPerClient').val() <= 0 ))
					errorMsg += '- Limit Number of Referrals per Client in Front-End should be greater than 0. <br>';
				
				if( $('##feDspLimitNumOfReferrals').is(':checked') &&  $.trim($('##feFreqRefPerClient').val()) == '') 
					errorMsg += '- Frequency. <br>';
				
				if( $('##feDspLimitNumOfReferrals').is(':checked') &&  $.trim($('##feIntakeFieldID').val()) == '') 
					errorMsg += '- Intake Field to Track. <br>';
				
				if( $('##feDspLimitNumOfReferrals').is(':checked') &&  $.trim($('##feDuplicateReferralTxt').val()) == '') 
					errorMsg += '- Warning Message. <br>';
			}

			if ($('##feLegalDescLimitWords').length && $('##feLegalDescLimitWords').is(':checked')) {
				if($.trim($('##feLegalDescLimitWordCount').val()) == '' || !Number.isInteger(Number($('##feLegalDescLimitWordCount').val()))) 
					errorMsg += '- Enter a valid whole number in the Limit Word Count in the Legal Issue Description. <br>';
				
				if($.trim($('##feLegalDescLimitExceedMsg').val()) == '') 
					errorMsg += '- Enter the Warning Message in the Legal Issue Description. <br>';
			}

			_notFrontEndCheckedGeneral = false;
			$('input[name^=isRequired_').each(function(){
				if($(this).is(':checked')){

					_id = $(this).attr('name').split('_')[1];
					if(!$('##isFrontEndDisplay_'+_id).is(':checked'))
						_notFrontEndCheckedGeneral = true;
				}
			});
			if(_notFrontEndCheckedGeneral)
				errorMsg += '- Any of the fields set to required should also be set to display on front end.';

			if ($('##allowFeeDiscrepancy').is(':checked') == true){
				if($('##feeDiscrepancyReferralStatusIDs').val() == null)
					errorMsg += '- At least ONE Statuses to assess for Fee Discrepancies is required.'; 
				if (($('##feeDiscrepancyAmt').val().trim() == '' || isNaN($('##feeDiscrepancyAmt').val().trim())))
					errorMsg += '- Enter valid Fee Discrepancy Threshold Amount.'; 
			}
			
			return errorMsg;
		}
		function selectMemberCF(fldID,altTitle) {
			var selhref = '#local.memSelectLink#&mode=direct&autoClose=0&retFunction=top.selectAssignedTo&fldName=' + fldID + (altTitle ? '&dispTitle=' + escape(altTitle) : '');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: altTitle,
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
			
		}
		function selectAssignedTo(fldID,mID,mNum,mName) {
			if(fldID == "clientFeeMemberID"){
				$('##clientFeeMemberID').val(mID);
				$('##clientFeeMemberIDVal').text(mName + ' (' + mNum + ')');
			}else{
				$('##feCounselorID').val(mID);
				$('##feCounselorIDVal').text(mName + ' (' + mNum + ')');
			}
			MCModalUtils.hideModal();
		}	
		function updateField(fldID, mID, mNum, mName) {
			var fld = document.getElementById(fldID);
			if (fld) {
				fld.value = mID;
				var fldName = document.getElementById(fldID + 'Val');
				if (fldName) fldName.innerHTML = mName;
				chkClearDisplay(fldID, (fldID + 'Clear'));
			} else {
				alert('Unable to set ' + fldID + ' to ' + mID);
			}
		}
		function chkClearDisplay(fldID, fldClear) {
			var fld = document.getElementById(fldID);
			var fldClear = document.getElementById(fldClear);
			if (fld && fldClear) {
				if (fld.value == '') fldClear.style.display = 'none';
				else fldClear.style.display = 'block';
			}
		}           
		function cancelSettings() {
			window.location.href = "#local.link.referralSettings#";
		}
		function ctrlAllowPanelMgmt() {
			if ($('##dspPanelList').is(':checked') == true) { 
				$('##allowPanelMgmtRow').removeClass('d-none');
				$('##rcPanelInstructionsTxtRow').removeClass('d-none');
			} else {	
				$('##allowPanelMgmtRow').addClass('d-none');
				$('##rcPanelInstructionsTxtRow').addClass('d-none');
				$('##allowPanelMgmt').prop('checked',false);
				ctrlPanelMgmtRows();
			}
		}
		function ctrlPanelMgmtRows() {
			if ($('##allowPanelMgmt').is(':checked') == true) showPanelMgmtRows();
			else hidePanelMgmtRows();
		}
		function showPanelMgmtRows() {
			$('##maxNumOfPanelRow').removeClass('d-none');
			$('##panelStatusRow').removeClass('d-none');
			$('##panelAppStatusRow').removeClass('d-none');
		}
		function hidePanelMgmtRows() {
			$('##maxNumOfPanelRow').addClass('d-none');
			$('##panelStatusRow').addClass('d-none');
			$('##panelAppStatusRow').addClass('d-none');
		}
		function ctrlFeeTypeRows() {
			if ($('##allowFeeTypes').is(':checked') == true) showFeeTypeRows();
			else hideFeeTypeRows();
		}
		function showFeeTypeRows() {
			$('##feeTypeListRow').removeClass('d-none');
		}
		function hideFeeTypeRows() {
			$('##feeTypeListRow').addClass('d-none');
		}
		function toggleFeeDiscrepancyRow() {
			$('.feeDiscrepancy').toggleClass('d-none');
		}
		function manageLegalIssueDescWordLimits() {
			if($("##feLegalDescLimitWords").prop("checked")) $(".feLegalDescLimitWordsRow").show();
			else $(".feLegalDescLimitWordsRow").hide();
		}
		function updateGroupField(fldID,gID,gPath) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedVal');		
			fld.val(gID);
			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");	
				$('##associatedGroupName').val(newgPath);
				fldName.html(newgPath);
				$('##divAssociatedVal').show();
				$("##aClearAssocType").removeClass('hidden');                
				$('##aClearAssocType').addClass('show');
				$("##memEmailOptOutGroupIcon").removeClass('hidden');
				$("##memEmailOptOutGroupIcon").addClass('show');                  
				if(fld.val() != parseInt(#val(local.memEmailOptOutGroupID)#)){
					$("##groupSaveChangesTxt").removeClass('hidden');  
					$("##groupSaveChangesTxt").addClass('show');
					$('##memEmailOptOutGroupBtn').show(); 
				} else if(fld.val() == parseInt(#val(local.memEmailOptOutGroupID)#) || parseInt(#val(local.memEmailOptOutGroupID)#) == 0){
					$("##groupSaveChangesTxt").removeClass('show');  
					$("##groupSaveChangesTxt").addClass('hidden');
				}
				$('##memEmailOptOutGroupBtn').hide();
			} else {
				fldName.html('');
				$('##divAssociatedVal').hide();
			}
		}
		function updateCounselorAsgnmtGroupField(fldID,gID,gPath) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedCounselorGrpVal');		
			fld.val(gID);
			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");	
				$('##associatedCounselorGroupName').val(newgPath);
				fldName.html(newgPath);
				$('##divAssociatedCounselorGrpVal').show();
				$("##aClearCounselorGroupSelect").removeClass('hidden');                
				$('##aClearCounselorGroupSelect').addClass('show');
				$("##counselorAsgnmtGroupIcon").removeClass('hidden');
				$("##counselorAsgnmtGroupIcon").addClass('show');                  
				if(fld.val() != parseInt(#val(local.counselorGroupID)#)){
					$("##counselorGroupSaveChangesTxt").removeClass('hidden');  
					$("##counselorGroupSaveChangesTxt").addClass('show');
					$('##counselorAsgnmtGroupBtn').show(); 
				} else if(fld.val() == parseInt(#val(local.counselorGroupID)#) || parseInt(#val(local.counselorGroupID)#) == 0){
					$("##counselorGroupSaveChangesTxt").removeClass('show');  
					$("##counselorGroupSaveChangesTxt").addClass('hidden');
				}
				$('##counselorAsgnmtGroupBtn').hide();
			} else {
				fldName.html('');
				$('##divAssociatedCounselorGrpVal').hide();
			}
		}
		function removeFeeStructureRow(item){
			$('table##feeStructureTbl tr##'+item).remove();
			setFeeStructureChange();
		}
		function setFeeStructureChange(){
			$('##feeStructureChanged').val("true");
		}
		function showMsg(msg){
			$('##mainSettingErrMsg').html(msg);
			$('##mainSettingErrMsgWrapper').show();
			scrollTo($('##mainSettingErrMsgWrapper'));
		}
		function showIntakeErrMsg(msg){
			$('##intakeErrMsg').html(msg);
			$('##intakeErrMsgWrapper').show();
			scrollTo($('##intakeErrMsgWrapper'));
		}	
		function scrollTo(_obj){
			$('html, body').animate({
					scrollTop: _obj.offset().top -200
			}, 1000);
		}
		function toggleCoordNotifySettingsClient(x) {
			if ($(x).prop('checked')) {
				$('##CoordNtyToClientTemplateSettings').removeClass('d-none');
			} else {
				$('##CoordNtyToClientTemplateSettings').addClass('d-none');
			}
		}
		function toggleCoordNotifySettingsMember(x) {
			if ($(x).prop('checked')) {
				$('##CoordNtyToMemberTemplateSettings').removeClass('d-none');
			} else {
				$('##CoordNtyToMemberTemplateSettings').addClass('d-none');
			}
		}
				
		$(function() {
			mca_setupSelect2ByID('feIntakeFieldID');
    
			$('.nav-pills a').on('show.bs.tab', function(e){
				var id = $(this).attr('id').split("-")[0];
				$("##currTab").val(id);
				if (!gridInitArray[id]) {
					gridInitArray[id] = true;
					switch(id) {
						case "emailNotifications":
							setTimeout(() => {
								mcActivateBSToggle($('##emailNotifications'));
							}, 1000);
							break;
						case "referralCenter":
							initReferralFeeTypesTable(); break;
						case "emailTemplates":
							<cfloop array="#local.arrReferralET#" index="local.strETData">
								mcet_initEmailTemplatesTable('#local.strETData.gridExt#');
							</cfloop>
							break;
					}
				}
			});
			
			$('.templateSelect').on('change',function(){
				pElem = $(this).next('.previewLink');
				templateID = $(this).val().trim();
				template = $(this).data('template').trim();
				if(templateID == ''){
					pElem.addClass('hidden');
					$('.'+template).removeClass('hidden');
				}else{
					pElem.removeClass('hidden');
					$('.'+template).addClass('hidden');
				}			
				pElem.attr('data-templateid',templateID);
			});	
			$('.previewLink').on('click',function(){
				let template = $(this).data('template');
				let templateId = $(this).data('templateid');
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					title: 'Preview Email',
					iframe: true,
					contenturl: '#this.link.previewReferralEmail#&template=' + template +'&templateId='+templateId,
					strmodalfooter : {
					showclose: false,
					classlist: 'd-flex',
					buttons: [
						{
							class:"btn btn-sm btn-primary previewContinueBtn",
							clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnContinue").click',
							label: 'Continue', 
							name: 'btnContinue',
							id: 'btnContinue'
						},
						{
							class: "btn btn-primary btn-sm d-none",
							clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnTestTemplate").click',
							label: 'Send Test E-mail', 
							name: 'btnTestTemplate',
							id: 'btnTestTemplate'
						},
						{
							class: "btn btn-primary btn-sm d-none",
							label: 'Sending Email...', 
							name: 'btnEmailSend',
							id: 'btnEmailSend'
						}
					]
				}
				});
			});	

			$('##btnSave').click(function(){
				var errorMsg = "";
				errorMsg = validateSettingsFrm();

				if (errorMsg.length > 0) {
					showMsg("There were errors with your submission.<br>" + errorMsg);
				} else {
					$('##mainSettingErrMsgWrapper').hide();
					$('##btnSave').prop('disabled',true);
					$('##formSettings').submit();
				}
			});            

			$('##feDspQuestionTree').change(function(){
				if($(this).is(':checked')){
					$('.questionRelated').show();
				} else {
					$('.questionRelated').hide();
				}
			});
			
			$('##feDspSurveyOption').change(function(){
				if($(this).is(':checked')){
					$('##feSurveyOptionDefaultYesWrapper').show();
				} else {
					$('##feSurveyOptionDefaultYesWrapper').hide();
				}
			});

			mca_setupSelect2();
			mca_setupTagsInput('emailRecipient', 'err_emailRecipient', "#application.regEx.email#", 'email address');
			
			ctrlAllowPanelMgmt();
			ctrlPanelMgmtRows();
			ctrlFeeTypeRows();

			if($('##feDspQuestionTree').is(':checked')){
				$('.questionRelated').show();
			} else {
				$('.questionRelated').hide();
			}
			if($('##feDspSurveyOption').is(':checked')){
				$('##feSurveyOptionDefaultYesWrapper').show();
			} else {
				$('##feSurveyOptionDefaultYesWrapper').hide();
			}
			
			$(".addRow").click(function(){
				var tableRowCount = $('##feeStructureTbl tr').length + 1;
				var feeStructureRow = '#local.feeStructureRow#';
				var rowClass = "ev_modern";  
				
				if (tableRowCount % 2 === 0)
					rowClass = "odd_modern"; 

				feeStructureRow = feeStructureRow.replace('<tr id="0">', '<tr id="tr' + tableRowCount + '" class="' + rowClass + '">');
				feeStructureRow = feeStructureRow.replace("removeFeeStructureRow()", "removeFeeStructureRow('tr" + tableRowCount + "')");
				$('##feeStructureTbl tr:last').after(feeStructureRow);
				setFeeStructureChange();
			});	

			$(".removeRow").click(function(){
				var trid = $(this).closest('tr').attr('id');
				removeFeeStructureRow(trid);
			});	

			$('.feeStructField').change(function(){
				setFeeStructureChange();
			});
			$('input[name^=isRequired_]').click(function(){
				if($(this).is(':checked') ){
					var _id = $(this).attr('name').split('_')[1];
					$('##isFrontEndDisplay_'+_id).attr('checked','checked');
				}
			});

			$("##emailAgencyInfoToClient").click(function(){                
				var _this = $(this);
				if(_this.prop("checked")){
					$(".sendAgencyToClient").show();
				} else {
					$(".sendAgencyToClient").hide();
				}
			});  

			<cfif local.emailAgencyInfoToClient>
				$(".sendAgencyToClient").show();
			<cfelse>
				$(".sendAgencyToClient").hide();
			</cfif>

			$("##feDspLimitNumOfReferrals").click(function(){                
				var _this = $(this);
				if(_this.prop("checked")){
					$(".clFeDspLimitNumOfReferrals").show();
				} else {
					$(".clFeDspLimitNumOfReferrals").hide();
				}
			});

			<cfif val(local.feSiteResourceID) and local.feDspLimitNumOfReferrals>
				$(".clFeDspLimitNumOfReferrals").show();

				$('input[name^="isRequired_"]').change(function(){
					var _this = $(this);
					var _trackField = $('##feIntakeFieldID').val();
					if(!_this.is(':checked') && _trackField == _this.data('field')){
						showIntakeErrMsg($('##feIntakeFieldID option:selected').text() + ' is no longer a required field. Please select another field for Intake Field to Track.');
					} else {
						$('##intakeErrMsgWrapper').hide();
					}
				});                
			<cfelse>
				$(".clFeDspLimitNumOfReferrals").hide();
			</cfif>  

			var #ToScript(local.grpSelectGotoLink,"link_grpSelectGotoLink")#
			$("##memEmailOptOutGroupBtn, ##memEmailOptOutGroupIcon").on("click",function(){	
				var selhref = link_grpSelectGotoLink+'&mode=direct&fldName=memEmailOptOutGroupID&retFunction=top.updateGroupField&dispTitle=' + escape('Opt Out from Notifications');
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'xl',
					title: 'Opt Out from Notifications',
					iframe: true,
					contenturl: selhref,
					strmodalfooter: {
						classlist: 'd-none'
					}
				});
			});
			$("##counselorAsgnmtGroupBtn, ##counselorAsgnmtGroupIcon").on("click",function(){	
				var selhref = link_grpSelectGotoLink+'&mode=direct&fldName=counselorGroupID&retFunction=top.updateCounselorAsgnmtGroupField&dispTitle=' + escape('Counselor Assignment Group');
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'xl',
					title: 'Counselor Assignment Group',
					iframe: true,
					contenturl: selhref,
					strmodalfooter: {
						classlist: 'd-none'
					}
				});
			});

			$("##purgePanelsBtn").on("click",function(){	
				purgePanels();
			});

			$("##aClearAssocType").live("click",function() {
				$('##associatedVal').html("");
				$('##memEmailOptOutGroupID').val(0);
				$('##associatedGroupName').val('');
				$('##divAssociatedVal').hide();
				$('##memEmailOptOutGroupBtn').show();
				$('##memEmailOptOutGroup').text("Select Group"); 
				if($('##memEmailOptOutGroupID').val() == parseInt(#val(local.memEmailOptOutGroupID)#) || parseInt(#val(local.memEmailOptOutGroupID)#) == 0){
					$("##groupSaveChangesTxt").removeClass('show');  
					$("##groupSaveChangesTxt").addClass('hidden'); 
				}
				if(($('##memEmailOptOutGroupID').val() == 0 && $('##memEmailOptOutGroupID').val() != parseInt(#val(local.memEmailOptOutGroupID)#)) || parseInt(#val(local.memEmailOptOutGroupID)#) > 0){
					$("##groupSaveChangesTxt").removeClass('hidden');  
					$("##groupSaveChangesTxt").addClass('show');
				}
			});
			$("##aClearCounselorGroupSelect").live("click",function() {
				$('##associatedCounselorGrpVal').html("");
				$('##counselorGroupID').val(0);
				$('##associatedGroupName').val('');
				$('##divAssociatedCounselorGrpVal').hide();
				$('##counselorAsgnmtGroupBtn').show();
				$('##counselorAsgnmtGroup').text("Select Group"); 
				if($('##counselorGroupID').val() == parseInt(#val(local.counselorGroupID)#) || parseInt(#val(local.counselorGroupID)#) == 0){
					$("##counselorGroupSaveChangesTxt").removeClass('show');  
					$("##counselorGroupSaveChangesTxt").addClass('hidden'); 
				}
				if(($('##counselorGroupID').val() == 0 && $('##counselorGroupID').val() != parseInt(#val(local.counselorGroupID)#)) || parseInt(#val(local.counselorGroupID)#) > 0){
					$("##counselorGroupSaveChangesTxt").removeClass('hidden');  
					$("##counselorGroupSaveChangesTxt").addClass('show');
				}
			});

			<cfif val(local.memEmailOptOutGroupID)>
				$('##divAssociatedVal').show();                
				$("##aClearAssocType").removeClass('hidden');               
				$('##aClearAssocType').addClass('show');
				$("##memEmailOptOutGroupIcon").removeClass('hidden');
				$("##memEmailOptOutGroupIcon").addClass('show');     
				$('##memEmailOptOutGroupBtn').hide();         
			<cfelse>
				$('##divAssociatedVal').hide();
				$('##memEmailOptOutGroupBtn').show(); 
			</cfif>    

			<cfif val(local.counselorGroupID)>
				$('##divAssociatedCounselorGrpVal').show();                
				$("##aClearCounselorGroupSelect").removeClass('hidden');               
				$('##aClearCounselorGroupSelect').addClass('show');
				$("##counselorAsgnmtGroupIcon").removeClass('hidden');
				$("##counselorAsgnmtGroupIcon").addClass('show');     
				$('##counselorAsgnmtGroupBtn').hide();
			<cfelse>
				$('##divAssociatedCounselorGrpVal').hide();
				$('##counselorAsgnmtGroupBtn').show(); 
			</cfif>

			manageLegalIssueDescWordLimits();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.setttingsJS)#">

<cfset local.selectedTab = event.getTrimValue("tab","mainSettings")>

<cfoutput>
	
<form name="formSettings" id="formSettings" method="post" action="#this.link.manageMainSettings#" autocomplete="off">
	<input type="hidden" name="searchUseID" value="#local.searchUseID#">
	<input type="hidden" name="resultsUseID" value="#local.resultsUseID#">
	<input type="hidden" name="clientFeeResultsUseID" value="#local.clientFeeResultsUseID#">
	<input type="hidden" name="clientEmailConfirmationUseID" value="#local.clientEmailConfirmationUseID#">
	<input type="hidden" name="checklistUseID" value="#local.checklistUseID#">
	<input type="hidden" name="referralsSMS" value="#this.referralsSMS#">
	<cfif val(local.feSiteResourceID)>
		<input type="hidden" name="clientSearchUseID" value="#local.clientSearchUseID#">
		<input type="hidden" name="clientResultsUseID" value="#local.clientResultsUseID#">
		<input type="hidden" name="feApplicationInstanceID" value="#this.feApplicationInstanceID#">
		<input type="hidden" name="fePanelInfoContentID" value="#local.fePanelInfoContentID#">
		<input type="hidden" name="feNoResultsInfoContentID" value="#local.feNoResultsInfoContentID#">
		<input type="hidden" name="feLegalDescInstructContentID" value="#local.feLegalDescInstructContentID#">
		<input type="hidden" name="feFormInstructionsContentID" value="#local.feFormInstructionsContentID#">
		<input type="hidden" name="feFormInstructionsStep1ContentID" value="#local.feFormInstructionsStep1ContentID#">
		<input type="hidden" name="feFormInstructionsStep2ContentID" value="#local.feFormInstructionsStep2ContentID#">
		<input type="hidden" name="feFormInstructionsStep3ContentID" value="#local.feFormInstructionsStep3ContentID#">
		<input type="hidden" name="feFormInstructionsStep4ContentID" value="#local.feFormInstructionsStep4ContentID#">
		<input type="hidden" name="feSuccessfulResultsInstructionsContentID" value="#local.feSuccessfulResultsInstructionsContentID#">
	</cfif>
	<input type="hidden" name="siteResourceID" value="#this.siteResourceID#">
	<input type="hidden" name="feSiteResourceID" value="#local.feSiteResourceID#">
	<input type="hidden" name="referralID" value="#this.referralID#">
	<input type="hidden" name="applicationInstanceID" value="#this.applicationInstanceID#">
	<input type="hidden" name="tab" id="currTab" value="#local.selectedTab#">	
	<input type="hidden" name="saveFrm" value="1">
	<input type="hidden" name="feeStructureChanged"  id="feeStructureChanged" value="false">
	
	<h4>
	Main Settings
	<button class="btn btn-sm btn-secondary float-right" type="button" onClick="cancelSettings();">Cancel</button>
	<button class="btn btn-sm btn-primary float-right" type="button" name="btnSave" id="btnSave">Save</button>
	</h4>
		
	<div class="alert alert-danger alert-dismissible fade show hidden" role="alert" id="mainSettingErrMsgWrapper">
		<span id="mainSettingErrMsg"></span>
		<button type="button" class="close" aria-label="Close" data-hide="alert">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>

	<br>		
	<cfif event.getTrimValue("lockTab","false")>
		<cfset local.lockTab = local.selectedTab>
	<cfelse>
		<cfset local.lockTab = "">
	</cfif>
	<cfset local.activeSelectedTab = ""/>
	<ul class="nav nav-pills nav-pills-dotted" data-mcCollapsibleDivChangeHandler="onTabChangeHandler" id="settingsTab" role="tablist">
		<cfset local.thisTab = "mainSettings">
		<cfset local.thisView = "mainSettings">
		<cfset local.isSelected = listFindNoCase(local.selectedTab,local.thisTab)>
		<cfset local.isLocked = listFindNoCase(local.lockTab,local.thisTab)>
		<cfif NOT local.isLocked AND local.isSelected>
			<cfset local.activeSelectedTab = local.selectedTab/>
		</cfif>
		<li class="nav-item"><a 
			id="#local.thisView#-Tab"
			data-toggle="tab" href="###local.thisTab#" role="tab" aria-controls="#local.thisTab#" aria-selected="#BooleanFormat(local.isSelected)#"
			class="nav-link <cfif local.isLocked> disabled<cfelseif local.isSelected> active</cfif>">Main Settings</a></li>    

		<cfset local.thisTab = "emailNotifications">
		<cfset local.thisView = "emailNotifications">
		<cfset local.isSelected = listFindNoCase(local.selectedTab,local.thisTab)>
		<cfset local.isLocked = listFindNoCase(local.lockTab,local.thisTab)>
		<cfif NOT local.isLocked AND local.isSelected>
			<cfset local.activeSelectedTab = local.selectedTab/>
		</cfif>
		<li class="nav-item"><a
			id="#local.thisView#-Tab"
			data-toggle="tab" href="###local.thisTab#" role="tab" aria-controls="#local.thisTab#" aria-selected="#BooleanFormat(local.isSelected)#"
			class="nav-link <cfif local.isLocked> disabled<cfelseif local.isSelected> active</cfif>">E-mail Notifications</a>
			
		<cfif this.referralsSMS>	
			<cfset local.thisTab = "frontEndReferralsSMS">
			<cfset local.thisView = "frontEndReferralsSMS">
			<cfset local.isSelected = listFindNoCase(local.selectedTab,local.thisTab)>
			<cfset local.isLocked = listFindNoCase(local.lockTab,local.thisTab)>
			<cfif NOT local.isLocked AND local.isSelected>
				<cfset local.activeSelectedTab = local.selectedTab/>
			</cfif>
			<li class="nav-item"><a
				id="#local.thisView#-Tab"
				data-toggle="tab" href="###local.thisTab#" role="tab" aria-controls="#local.thisTab#" aria-selected="#BooleanFormat(local.isSelected)#"
				class="nav-link <cfif local.isLocked> disabled<cfelseif local.isSelected> active</cfif>">Text Messages</a></li>	
		</cfif>		

		<cfset local.thisTab = "panelSettings">
		<cfset local.thisView = "panelSettings">
		<cfset local.isSelected = listFindNoCase(local.selectedTab,local.thisTab)>
		<cfset local.isLocked = listFindNoCase(local.lockTab,local.thisTab)>
		<cfif NOT local.isLocked AND local.isSelected>
			<cfset local.activeSelectedTab = local.selectedTab/>
		</cfif>
		<li class="nav-item"><a
			id="#local.thisView#-Tab"
			data-toggle="tab" href="###local.thisTab#" role="tab" aria-controls="#local.thisTab#" aria-selected="#BooleanFormat(local.isSelected)#"            
			class="nav-link <cfif local.isLocked> disabled<cfelseif local.isSelected> active</cfif>">Panel Settings</a></li>

		<cfset local.thisTab = "referralCenter">
		<cfset local.thisView = "referralCenter">
		<cfset local.isSelected = listFindNoCase(local.selectedTab,local.thisTab)>
		<cfset local.isLocked = listFindNoCase(local.lockTab,local.thisTab)>
		<cfif NOT local.isLocked AND local.isSelected>
			<cfset local.activeSelectedTab = local.selectedTab/>
		</cfif>
		<li class="nav-item"><a
			id="#local.thisView#-Tab"
			data-toggle="tab" href="###local.thisTab#" role="tab" aria-controls="#local.thisTab#" aria-selected="#BooleanFormat(local.isSelected)#"            
			class="nav-link <cfif local.isLocked> disabled<cfelseif local.isSelected> active</cfif>">Referral Center</a></li>	

		<cfif this.feApplicationInstanceID>	
			<cfset local.thisTab = "frontEndReferrals">
			<cfset local.thisView = "frontEndReferrals">
			<cfset local.isSelected = listFindNoCase(local.selectedTab,local.thisTab)>
			<cfset local.isLocked = listFindNoCase(local.lockTab,local.thisTab)>
			<cfif NOT local.isLocked AND local.isSelected>
				<cfset local.activeSelectedTab = local.selectedTab/>
			</cfif>
			<li class="nav-item"><a
				id="#local.thisView#-Tab"
				data-toggle="tab" href="###local.thisTab#" role="tab" aria-controls="#local.thisTab#" aria-selected="#BooleanFormat(local.isSelected)#"
				class="nav-link <cfif local.isLocked> disabled<cfelseif local.isSelected> active</cfif>">Front-End Referrals</a></li>	
		</cfif>

		<cfset local.thisTab = "emailTemplates">
		<cfset local.thisView = "emailTemplates">
		<cfset local.isSelected = listFindNoCase(local.selectedTab,local.thisTab)>
		<cfset local.isLocked = listFindNoCase(local.lockTab,local.thisTab)>
		<cfif NOT local.isLocked AND local.isSelected>
			<cfset local.activeSelectedTab = local.selectedTab/>
		</cfif>
		<li class="nav-item">
			<a id="#local.thisView#-Tab"
			data-toggle="tab" href="###local.thisTab#" role="tab" aria-controls="#local.thisTab#" aria-selected="#BooleanFormat(local.isSelected)#"
			class="nav-link <cfif local.isLocked> disabled<cfelseif local.isSelected> active</cfif>">Email Templates</a></li>
		
		<cfset local.thisTab = "intakeManagement">
		<cfset local.thisView = "intakeManagement">
		<cfset local.isSelected = listFindNoCase(local.selectedTab,local.thisTab)>
		<cfset local.isLocked = listFindNoCase(local.lockTab,local.thisTab)>
		<cfif NOT local.isLocked AND local.isSelected>
			<cfset local.activeSelectedTab = local.selectedTab/>
		</cfif>
		<li class="nav-item">
			<a id="#local.thisView#-Tab"
			data-toggle="tab" href="###local.thisTab#" role="tab" aria-controls="#local.thisTab#" aria-selected="#BooleanFormat(local.isSelected)#"
			class="nav-link <cfif local.isLocked> disabled<cfelseif local.isSelected> active</cfif>">Intake Management</a></li>
	</ul>
	<div class="tab-content mc_tabcontent p-3">
		<div id="mainSettings" class="tab-pane <cfif local.activeSelectedTab EQ "mainSettings"> active</cfif>">
			<div class="form-group row">
				<label for="title" class="col-sm-4 col-form-label">Title</label>
				<div class="col-sm-5 align-self-center">
					<input name="title"  id="title" type="text" size="60" maxlength="255" value="#local.title#" class="form-control form-control-sm"/>
				</div>
			</div>
			<div class="form-group row align-items-center mb-2">
				<div class="col-sm-4">Field Set for Filter Tab</div>
				<div class="col-sm-5">
					#local.strSearchFSSelector.html#
				</div>
			</div>
			<div class="form-group row align-items-center mb-2">
				<div class="col-sm-4">Field Set for Lawyer Tab</div>
				<div class="col-sm-5">
					#local.strResultsFSSelector.html#
				</div>
			</div>
			<div class="form-group row align-items-center mb-2">
				<div class="col-sm-4">Field Set for Lawyer Preview</div>
				<div class="col-sm-5">
					#local.strClientFeeFSSelector.html#
				</div>
			</div>
			<div class="form-group row align-items-center mb-2">
				<div class="col-sm-4">Email Confirmation Field Set</div>
				<div class="col-sm-5">
					#local.strEmailConfirmationFSSelector.html#
				</div>
			</div>
			<div class="form-group row align-items-center mb-2">
				<div class="col-sm-4">Field Set for Panel Member Checklist</div>
				<div class="col-sm-5">
					#local.strCheckListFSSelector.html#
				</div>
			</div>
			<div class="form-group row">
				<label for="defaultStateID" class="col-sm-4 col-form-label">Default State for Client Information</label>
				<div class="col-sm-5 align-self-center">
					<select name="defaultStateID" id="defaultStateID" class="form-control form-control-sm">
						<option value=""></option>
						</cfoutput>
						<cfoutput query="local.qryStates" group="countryID">
						<optgroup label="#local.qryStates.country#">
						<cfoutput>
							<option value="#local.qryStates.stateid#" <cfif local.qryStates.stateid eq local.defaultStateID>selected</cfif>>#local.qryStates.stateName# &nbsp;</option>
						</cfoutput>
						</optgroup>
						</cfoutput>
						<cfoutput>
					</select>
				</div>
			</div>
			<div class="form-group row">
				<label for="defaultCity" class="col-sm-4 col-form-label">Default City for Client Information</label>
				<div class="col-sm-5 align-self-center">
					<input name="defaultCity"  id="defaultCity" type="text" size="30" maxlength="100" value="#local.defaultCity#" class="form-control form-control-sm"/>
				</div>
			</div>
			<div class="form-group row">
				<label for="defaultCallType" class="col-sm-4 col-form-label">Default Call Type</label>
				<div class="col-sm-5 align-self-center">
					<select name="defaultCallType" id="defaultCallType" class="form-control form-control-sm"  >
						<option value=""></option>
						<cfloop query="local.qryGetReferralTypes">
							<option value="#local.qryGetReferralTypes.clientReferralTypeID#" <cfif local.qryGetReferralTypes.clientReferralTypeID eq local.defaultCallType or (local.qryGetReferralTypes.recordCount EQ 1 AND val(local.defaultCallType) EQ "0")>selected</cfif>>#local.qryGetReferralTypes.clientReferralType#</option>
						</cfloop>
					</select>
				</div>
			</div>
			<div class="form-group row">
				<label for="defaultClientSurvey" class="col-sm-4 col-form-label">Default for Client Survey</label>
				<div class="col-sm-5 align-self-center">
					<select name="defaultClientSurvey" id="defaultClientSurvey" class="form-control form-control-sm"  >
						<option value="0"></option>
						<option value="1" <cfif val(local.defaultClientSurvey)>selected</cfif>>Yes</option>
					</select>
				</div>
			</div>
			<div class="form-group">
				#local.strReferralGLAcctWidget.html#
			</div>
			<div class="form-group">
				#local.strClientGLAcctWidget.html#
			</div>
			<div class="form-group row mt-2">
				<label for="clientFeeMemberID" class="col-sm-4 col-form-label pt-0">Member Account used for Client Fees</label>
				<div class="col-sm-5 align-self-center">
					<input type="hidden" name="clientFeeMemberID" id="clientFeeMemberID" value="#local.clientFeeMemberID#" />
					<div id="clientFeeMemberIDVal" class="float-left d-inline mr-4">#local.clientFeeMemberName#</div>  
					<div id="clientFeeMemberIDSelect" class="float-left d-inline mr-2"><a href="javascript:selectMemberCF('clientFeeMemberID','Select Member Account')">Select Member</a></div>
				</div>
			</div>
			<div class="form-group row">
				<div class="col-sm">
					#createObject("component","model.admin.common.modules.paymentProfileSelector.paymentProfileSelector").getPaymentProfileSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="refMPProfileIDs", selectedPaymentProfileIDList=local.payProfileList, fieldLabel="Payment Profiles", colCount=2, allowOffline=false, allowPayLater=true).html#
				</div>
			</div>

			<div class="form-group row mt-2">
				<label for="counselorAsgnmtGroup" class="col-sm-4 col-form-label pt-0">Counselor Assignment Group</label>
				<div class="col-sm-5">    
					<div id="divAssociatedCounselorGrpVal">
						<span id="associatedCounselorGrpVal" class="font-weight-bold">
							<cfif val(local.counselorGroupID)>
								#local.counselorAssignmentGroupPath#
							</cfif>
						</span>
						<a href="##" id="aClearCounselorGroupSelect" class="hidden" data-toggle="tooltip" data-placement="top" title="Clear Group" style="margin-left:15px;"><i class="fa-solid fa-eraser"></i></a> |
						<a href="##" id="counselorAsgnmtGroupIcon" class="hidden" data-toggle="tooltip" data-placement="top" title="Update Group"><i class='fa-solid fa-pen-to-square'></i></a>
					</div>           
					<a href="##" id="counselorAsgnmtGroupBtn" class="btn btn-primary btn-sm" role="button">Select Group</a>
					<div id="counselorGroupSaveChangesTxt" class="alert alert-warning alert-dismissible fade hidden" style="margin-top:10px;" role="alert">
						Hit <i>Save</i> in order to keep the changes.
					</div>
					<input type="hidden" name="counselorGroupID" id="counselorGroupID" value="#local.counselorGroupID#" />
					<input type="hidden" name="associatedCounselorGroupName" id="associatedCounselorGroupName" value="#local.memEmailOptOutGroupPath#">
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="rotationByPanel" class="col-sm-4 col-form-label pt-0">Member Rotation By Panel?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="rotationByPanel" id="rotationByPanel" value="1" class="form-check-input" <cfif local.rotationByPanel>checked="checked"</cfif>>
						<label class="form-check-label" for="rotationByPanel">Yes</label>
					</div>       
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="isPanelGroupDepend" class="col-sm-4 col-form-label pt-0">Panel Dependency on Groups?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="isPanelGroupDepend" id="isPanelGroupDepend" value="1" class="form-check-input" <cfif local.isPanelGroupDepend>checked="checked"</cfif>>
						<label class="form-check-label" for="isPanelGroupDepend">Yes</label>
					</div>       
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="dspImportClientReferralID" class="col-sm-4 col-form-label pt-0">Display Import Referral/Case ID?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="dspImportClientReferralID" id="dspImportClientReferralID" value="1" class="form-check-input" <cfif local.dspImportClientReferralID>checked="checked"</cfif>>
						<label class="form-check-label" for="dspImportClientReferralID">Yes</label>
					</div>       
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="deductFilingFee" class="col-sm-4 col-form-label pt-0">Deduct Filing Fee from Client Fee?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="deductFilingFee" id="deductFilingFee" value="1" class="form-check-input" <cfif local.deductFilingFee>checked="checked"</cfif>>
						<label class="form-check-label" for="deductFilingFee">Yes</label>
					</div>       
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="dspLegalDescription" class="col-sm-4 col-form-label pt-0">Display Legal Description on Client Referrals form?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="dspLegalDescription" id="dspLegalDescription" value="1" class="form-check-input" <cfif local.dspLegalDescription>checked="checked"</cfif>>
						<label class="form-check-label" for="dspLegalDescription">Yes</label>
					</div>       
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="collectClientFeeFE" class="col-sm-4 col-form-label pt-0">Collect the Client Fees in Referral Center</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="collectClientFeeFE" id="collectClientFeeFE" value="1" class="form-check-input" <cfif local.collectClientFeeFE>checked="checked"</cfif>>
						<label class="form-check-label" for="collectClientFeeFE">Yes</label>
					</div>       
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="allowFeeDiscrepancy" class="col-sm-4 col-form-label pt-0">Fee Discrepancies</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="allowFeeDiscrepancy" id="allowFeeDiscrepancy" value="1" class="form-check-input" <cfif local.allowFeeDiscrepancy>checked="checked"</cfif> onchange="toggleFeeDiscrepancyRow();">
						<label class="form-check-label" for="allowFeeDiscrepancy">Yes</label>
					</div>       
				</div>
			</div>
			<div class="form-group  row feeDiscrepancy <cfif NOT local.allowFeeDiscrepancy>d-none</cfif>">
				<label for="feeDiscrepancyReferralStatusIDs" class="col-sm-4 col-form-label">Statuses to assess for Fee Discrepancies</label>
				<div class="col-sm-5 align-self-center">
					<select name="feeDiscrepancyReferralStatusIDs" id="feeDiscrepancyReferralStatusIDs" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2" size="5" placeholder="Choose options below">
						<cfoutput query="local.qryGetClientReferralStatuses" group="primaryStatus">
							<optgroup label="#local.qryGetClientReferralStatuses.primaryStatus#">
								<cfoutput>
									<option 
										<cfif listFind(local.feeDiscrepancyReferralStatusIDs,local.qryGetClientReferralStatuses.clientReferralStatusID)>selected="selected"</cfif>
										value="#local.qryGetClientReferralStatuses.clientReferralStatusID#" 
										title="#local.qryGetClientReferralStatuses.statusName#">
										#local.qryGetClientReferralStatuses.statusName#<cfif local.qryGetClientReferralStatuses.isActive eq 0> (Inactive)</cfif>
									</option>
								</cfoutput>
							</optgroup>
						</cfoutput>
					</select>
				</div>
			</div>

			<div class="form-group row feeDiscrepancy  <cfif NOT local.allowFeeDiscrepancy>d-none</cfif>">
				<label for="feeDiscrepancyAmt" class="col-sm-4 col-form-label">Fee Discrepancy Threshold Amount <br><span style="font-size:85%;">% allowed standard deviation</span></label>
				<div class="col-sm-5 align-self-center">
					<input name="feeDiscrepancyAmt"  id="feeDiscrepancyAmt" type="text" size="60" maxlength="15" value="#local.feeDiscrepancyAmt#" class="form-control form-control-sm"/>
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="collectClientFeeFEOverrideTxt" class="col-sm-4 col-form-label">Consultation Fee Label Override Text</label>
				<div class="col-sm-5 align-self-center">
					<input name="collectClientFeeFEOverrideTxt"  id="collectClientFeeFEOverrideTxt" type="text" size="60" maxlength="255" value="#local.collectClientFeeFEOverrideTxt#" class="form-control form-control-sm"/>
				</div>
			</div>
		</div>
		<div id="emailNotifications" class="tab-pane <cfif local.activeSelectedTab EQ "emailNotifications"> active</cfif>">
			<div id="err_emailRecipient" class="alert alert-danger mb-2 d-none"></div>
			<div class="form-group row mt-2">
				<label for="emailRecipient" class="col-sm-4 col-form-label pt-0">Referral Notifications Reply-to E-mail</label>
				<div class="col-sm-5">
					<input name="emailRecipient" id="emailRecipient" type="text" value="#local.email#" class="form-control form-control-sm">      
				</div>
			</div>
			<div class="form-group row">
				<label for="emailTypes" class="col-sm-4 col-form-label">Send Lawyer E-mail Notifications to</label>
				<div class="col-sm-5 align-self-center">
					<select name="emailTypes" id="emailTypes" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2" size="5" placeholder="Choose options below">
						<cfloop query="local.qryGetOrgEmails">
							<option value="#local.qryGetOrgEmails.emailTypeID#" title="#local.qryGetOrgEmails.emailTypeDesc#" <cfif listFind(local.emailTypeList,local.qryGetOrgEmails.emailTypeID)>selected="selected"</cfif>>#local.qryGetOrgEmails.emailType#</option>
						</cfloop>
					</select>
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="clientEmailTemplate" class="col-sm-4 col-form-label pt-0">Referral Notifications to Client e-mail template</label>
				<div class="col-sm-5 align-self-center">
					<select name="clientEmailTemplate" data-template="refClientShow" id="clientEmailTemplate" class="form-control form-control-sm templateSelect">
						<option value="">Select a template</option>
						<cfset local.varDisplay = 'hidden'>
						<cfloop query="local.qryGetClientEmailTemplate">
							<option value="#local.qryGetClientEmailTemplate.templateID#"  <cfif local.qryGetClientEmailTemplate.templateID eq local.clientEmailTemplateId>selected="selected"</cfif>>#local.qryGetClientEmailTemplate.templateName#</option>
							<cfif val(local.clientEmailTemplateId) AND local.qryGetClientEmailTemplate.templateID eq local.clientEmailTemplateId>
								<cfset local.varDisplay = ''>
							</cfif>
						</cfloop>
					</select>
					<a class="previewLink #local.clientEmailTemplateId# #local.varDisplay#" data-template="clientReferral" data-templateid="#local.clientEmailTemplateId#" href="javascript:void(0);">Preview E-mail</a>
				</div>
			</div>
			<cfif local.varDisplay eq ''>
				<cfset local.varDisplay = 'hidden'>
			<cfelse>
				<cfset local.varDisplay = ''>
			</cfif>
			<div class="form-group row #local.varDisplay# refClientShow mt-2">
				<label for="clientMailTopTxt" class="col-sm-4 col-form-label pt-0">Referral Notifications to Client - Top Text</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="clientMailTopTxt" id="clientMailTopTxt"  rows="5" cols="60" class="form-control form-control-sm"/>#local.clientMailTopTxt#</textarea>
				</div>
			</div>
			<div class="form-group row #local.varDisplay# refClientShow mt-2">
				<label for="clientMailBottomTxt" class="col-sm-4 col-form-label pt-0">Referral Notifications to Client - Bottom Text</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="clientMailBottomTxt" id="clientMailBottomTxt"  rows="5" cols="60" class="form-control form-control-sm"/>#local.clientMailBottomTxt#</textarea>
				</div>
			</div>
			<cfset local.varDisplay = ''>
			<div class="form-group row mt-2">
				<label for="memberEmailTemplate" class="col-sm-4 col-form-label pt-0">Referral Notifications to Member E-mail template</label>
				<div class="col-sm-5 align-self-center">
					<select name="memberEmailTemplate" id="memberEmailTemplate" class="form-control form-control-sm templateSelect" data-template="refMemberShow">
						<option value="">Select a template</option>
						<cfset local.varDisplay = 'hidden'>
						<cfloop query="local.qryGetMemberEmailTemplate">
							<option value="#local.qryGetMemberEmailTemplate.templateID#"  <cfif local.qryGetMemberEmailTemplate.templateID eq local.memberEmailTemplateId>selected="selected"</cfif>>#local.qryGetMemberEmailTemplate.templateName#</option>
							<cfif local.qryGetMemberEmailTemplate.templateID eq local.memberEmailTemplateId>
								<cfset local.varDisplay = ''>
							</cfif>
						</cfloop>
					</select>
					<a class="previewLink #local.varDisplay#" data-template="memberReferral" data-templateid="#local.memberEmailTemplateId#" href="javascript:void(0);">Preview E-mail</a>				
				</div>
			</div>	
			<cfif local.varDisplay eq ''>
				<cfset local.varDisplay = 'hidden'>
			<cfelse>
				<cfset local.varDisplay = ''>
			</cfif>
			<div class="form-group row #local.varDisplay# refMemberShow mt-2">
				<label for="memberMailTopTxt" class="col-sm-4 col-form-label pt-0">Referral Notifications to Member - Top Text</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="memberMailTopTxt" id="memberMailTopTxt"  rows="5" cols="60" class="form-control form-control-sm"/>#local.memberMailTopTxt#</textarea>
				</div>
			</div>
			<div class="form-group row #local.varDisplay# refMemberShow mt-2">
				<label for="memberMailBottomTxt" class="col-sm-4 col-form-label pt-0">Referral Notifications to Member - Bottom Text</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="memberMailBottomTxt" id="memberMailBottomTxt"  rows="5" cols="60" class="form-control form-control-sm"/>#local.memberMailBottomTxt#</textarea>
				</div>
			</div>
			<cfif local.qryPendingCoordinationStatus.recordCount>
				<div class="form-group row mt-2">
					<label for="sendCoordNtyToClient" class="col-sm-4 col-form-label pt-0">Send Coordination Notifications to Client</label>
					<div class="col-sm-5 align-self-center">
						<input type="checkbox" name="sendCoordNtyToClient" id="sendCoordNtyToClient" value="1" class="form-check-input" data-toggle="toggle" data-size="sm" onchange="toggleCoordNotifySettingsClient(this);" autocomplete="off"<cfif val(local.qryReferralSettings.coordNtyToClientTemplateID)> checked</cfif>>
					</div>
				</div>
				<div id="CoordNtyToClientTemplateSettings"<cfif NOT val(local.qryReferralSettings.coordNtyToClientTemplateID)> class="d-none"</cfif>>
					<div class="form-group row mt-2 mb-3">
						<label for="coordNtyToClientTemplateID" class="col-sm-4 col-form-label pt-0">Referral Coordination Notifications to Client Email Template</label>
						<div class="col-sm-5">
							<select name="coordNtyToClientTemplateID" id="coordNtyToClientTemplateID" class="form-control form-control-sm">
								<option value="">Select a template</option>
								<cfloop query="local.qryPendingCoordClientEmailTemplate">
									<option value="#local.qryPendingCoordClientEmailTemplate.templateID#"<cfif local.qryPendingCoordClientEmailTemplate.templateID eq val(local.qryReferralSettings.coordNtyToClientTemplateID)> selected</cfif>>#local.qryPendingCoordClientEmailTemplate.templateName#</option>
								</cfloop>
							</select>
						</div>
					</div>
				</div>
				<div class="form-group row mt-2">
					<label for="sendCoordNtyToMember" class="col-sm-4 col-form-label pt-0">Send Coordination Notifications to Member</label>
					<div class="col-sm-5 align-self-center">
						<input type="checkbox" name="sendCoordNtyToMember" id="sendCoordNtyToMember" value="1" class="form-check-input" data-toggle="toggle" data-size="sm" onchange="toggleCoordNotifySettingsMember(this);" autocomplete="off"<cfif val(local.qryReferralSettings.coordNtyToMemberTemplateID)> checked</cfif>>
					</div>
				</div>
				<div id="CoordNtyToMemberTemplateSettings"<cfif NOT val(local.qryReferralSettings.coordNtyToMemberTemplateID)> class="d-none"</cfif>>
					<div class="form-group row mt-2 mb-3">
						<label for="coordNtyToMemberTemplateID" class="col-sm-4 col-form-label pt-0">Referral Coordination Notifications to Member Email Template</label>
						<div class="col-sm-5">
							<select name="coordNtyToMemberTemplateID" id="coordNtyToMemberTemplateID" class="form-control form-control-sm">
								<option value="">Select a template</option>
								<cfloop query="local.qryPendingCoordMemberEmailTemplate">
									<option value="#local.qryPendingCoordMemberEmailTemplate.templateID#"<cfif local.qryPendingCoordMemberEmailTemplate.templateID eq val(local.qryReferralSettings.coordNtyToMemberTemplateID)> selected</cfif>>#local.qryPendingCoordMemberEmailTemplate.templateName#</option>
								</cfloop>
							</select>
						</div>
					</div>
				</div>
			</cfif>
			<div class="form-group row mt-3">
				<label for="dspNewsletterLink" class="col-sm-4 col-form-label pt-0">Include Newsletter link?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="dspNewsletterLink" id="dspNewsletterLink" value="1" class="form-check-input" <cfif local.qryReferralSettings.dspNewsletterLink>checked="checked"</cfif>>
						<label class="form-check-label" for="dspNewsletterLink">Yes</label>
					</div>
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="clientReceiptEmailTemplate" class="col-sm-4 col-form-label pt-0">Client Receipt E-mail template</label>
				<div class="col-sm-5 align-self-center">
					<select name="clientReceiptEmailTemplate" id="clientReceiptEmailTemplate" class="form-control form-control-sm templateSelect" data-template="clientReceipt">
						<option value="">Select a template</option>
						<cfset local.varDisplay = 'hidden'>
						<cfloop query="local.qryGetClientReceipttEmailTemplate">
							<option value="#local.qryGetClientReceipttEmailTemplate.templateID#"  <cfif local.qryGetClientReceipttEmailTemplate.templateID eq local.receiptEmailTemplateId>selected="selected"</cfif>>#local.qryGetClientReceipttEmailTemplate.templateName#</option>
							<cfif local.qryGetClientReceipttEmailTemplate.templateID eq local.receiptEmailTemplateId>
								<cfset local.varDisplay = ''>
							</cfif>
						</cfloop>
					</select>
					<a class="previewLink #local.varDisplay#" data-template="clientReceiptReferral" data-templateid="#local.receiptEmailTemplateId#" href="javascript:void(0);">Preview E-mail</a>        
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="systemMailCaseActivityTxt" class="col-sm-4 col-form-label pt-0">Case Activity Notifications to Staff</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="systemMailCaseActivityTxt" id="systemMailCaseActivityTxt" class="form-control form-control-sm" rows="5" cols="60" />#local.systemMailCaseActivityTxt#</textarea>
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="memEmailOptOutGroup" class="col-sm-4 col-form-label pt-0">Lawyer Opt out of Referral Update Notice Group</label>
				<div class="col-sm-5">    
					<div id="divAssociatedVal">
						<span id="associatedVal" class="font-weight-bold">
							<cfif val(local.memEmailOptOutGroupID)>
								#local.memEmailOptOutGroupPath#
							</cfif>
						</span>
						<a href="##" id="aClearAssocType" class="hidden" data-toggle="tooltip" data-placement="top" title="Clear Group" style="margin-left:15px;"><i class="fa-solid fa-eraser"></i></a> |
						<a href="##" id="memEmailOptOutGroupIcon" class="hidden" data-toggle="tooltip" data-placement="top" title="Update Group"><i class='fa-solid fa-pen-to-square'></i></a>
					</div>           
					<a href="##" id="memEmailOptOutGroupBtn" class="btn btn-primary btn-sm pt-2" role="button">Select Group</a>
					<div id="groupSaveChangesTxt" class="alert alert-warning alert-dismissible fade hidden" style="margin-top:10px;" role="alert">
						Hit <i>Save</i> in order to keep the changes.
					</div>
					<input type="hidden" name="memEmailOptOutGroupID" id="memEmailOptOutGroupID" value="#local.memEmailOptOutGroupID#" />
					<input type="hidden" name="associatedGroupName" id="associatedGroupName" value="#local.memEmailOptOutGroupPath#">
				</div>
			</div>            
			<div class="form-group row mt-2">
				<label for="emailAgencyInfoToClient" class="col-sm-4 col-form-label pt-0">Send Agency Information to Client?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="emailAgencyInfoToClient" id="emailAgencyInfoToClient" value="1" class="form-check-input" <cfif local.emailAgencyInfoToClient>checked="checked"</cfif> />
						<label class="form-check-label" for="dspNewsletterLink">Yes</label>
					</div> 
				</div>
			</div>
			<div class="form-group row sendAgencyToClient mt-2">
				<label for="emailAgencyInfoToClientTopTxt" class="col-sm-4 col-form-label pt-0">Agency Notifications to Client - Top Text</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="emailAgencyInfoToClientTopTxt" id="emailAgencyInfoToClientTopTxt" class="form-control form-control-sm" rows="5" cols="60" />#local.emailAgencyInfoToClientTopTxt#</textarea>
				</div>
			</div>
			<div class="form-group row sendAgencyToClient mt-2">
				<label for="emailAgencyInfoToClientBottomTxt" class="col-sm-4 col-form-label pt-0">Agency Notifications to Client - Bottom Text</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="emailAgencyInfoToClientBottomTxt" id="emailAgencyInfoToClientBottomTxt" class="form-control form-control-sm" rows="5" cols="60" />#local.emailAgencyInfoToClientBottomTxt#</textarea>
				</div>
			</div>       	
		</div>
		<div id="panelSettings" class="tab-pane <cfif local.activeSelectedTab EQ "panelSettings"> active</cfif>">
			<div class="form-group row mt-2">
				<label for="dspPanelList" class="col-sm-4 col-form-label pt-0">Display Panel list in Referral Center?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="dspPanelList" id="dspPanelList" value="1" class="form-check-input" <cfif local.qryReferralSettings.dspPanelList>checked="checked"</cfif> onclick="javascript:ctrlAllowPanelMgmt();"/>
						<label class="form-check-label" for="dspPanelList">Yes</label>
					</div>        
				</div>
			</div>
			<div class="form-group row mt-2 d-none" id="rcPanelInstructionsTxtRow">
				<label for="rcPanelInstructionsTxt" class="col-sm-4 col-form-label pt-0">Panel Instructions</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="rcPanelInstructionsTxt" id="rcPanelInstructionsTxt" class="form-control form-control-sm" rows="5" cols="60" />#local.qryReferralSettings.rcPanelInstructionsTxt#</textarea>     
				</div>
			</div>
			<div class="form-group row mt-2 d-none" id="allowPanelMgmtRow">
				<label for="allowPanelMgmt" class="col-sm-4 col-form-label pt-0">Allow Panel management in Referral Center?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="allowPanelMgmt" id="allowPanelMgmt" class="form-check-input" value="1" <cfif local.qryReferralSettings.allowPanelMgmt>checked="checked"</cfif> onclick="javascript:ctrlPanelMgmtRows();"/>
						<label class="form-check-label" for="allowPanelMgmt">Yes</label>
					</div>        
				</div>
			</div>
			<div class="form-group row mt-2" id="dspEmailPanelListRow">
				<label for="dspEmailPanelListA" class="col-form-label col-sm-4 pt-0">Display Panel List in Confirmation E-Mail and Referral Results?</label>
				<div class="col-sm-5 align-self-center">
					<div class="form-check">
						<input class="form-check-input" type="radio" name="dspEmailPanelList" id="dspEmailPanelListA" value="A" <cfif local.qryReferralSettings.dspEmailPanelList EQ "A">checked="checked"</cfif> />
						<label class="form-check-label" for="dspEmailPanelListA">Show all panels</label>
					</div>
					<div class="form-check">
						<input class="form-check-input" type="radio" name="dspEmailPanelList" id="dspEmailPanelListS" value="S" <cfif local.qryReferralSettings.dspEmailPanelList EQ "S">checked="checked"</cfif> />
						<label class="form-check-label" for="dspEmailPanelListS">Only show selected panels</label>
					</div>
					<div class="form-check">
						<input class="form-check-input" type="radio" name="dspEmailPanelList" id="dspEmailPanelListN" value="N" <cfif local.qryReferralSettings.dspEmailPanelList EQ "N">checked="checked"</cfif> />
						<label class="form-check-label" for="dspEmailPanelListN">Show no panels</label>
					</div>
				</div>
			</div>
			<div class="form-group row d-none" id="maxNumOfPanelRow">
				<label for="maxNumOfPanel" class="col-sm-4 col-form-label">Maximum number of Panels</label>
				<div class="col-sm-5 align-self-center">
					<input type="text" name="maxNumOfPanel" id="maxNumOfPanel" value="#local.qryReferralSettings.maxNumberOfPanels#" class="form-control form-control-sm"/>
				</div>
			</div>
			<div class="form-group row d-none" id="panelStatusRow">
				<label for="panelStatus" class="col-sm-4 col-form-label">Panel Status</label>
				<div class="col-sm-5 align-self-center">
					<select name="panelStatus" id="panelStatus" class="form-control form-control-sm">
						<option value=""></option>
						<cfloop query="local.qryPanelStatuses">
							<option value="#local.qryPanelStatuses.panelMemberStatusID#" <cfif local.qryReferralSettings.panelMemberStatusID eq local.qryPanelStatuses.panelMemberStatusID>selected="selected"</cfif> >#local.qryPanelStatuses.statusName#</option>
						</cfloop>
					</select>
				</div>
			</div>
			<div class="form-group row d-none" id="panelAppStatusRow">
				<label for="panelAppStatus" class="col-sm-4 col-form-label">Panel Application Status</label>
				<div class="col-sm-5 align-self-center">
					<select name="panelAppStatus" id="panelAppStatus" class="form-control form-control-sm">
						<option value=""></option>
						<cfloop query="local.qryPanelAppStatuses">
							<option value="#local.qryPanelAppStatuses.panelMemberAppStatusID#" <cfif local.qryReferralSettings.panelMemberApplicationStatusID eq local.qryPanelAppStatuses.panelMemberAppStatusID>selected="selected"</cfif>>#local.qryPanelAppStatuses.statusName#</option>
						</cfloop>
					</select>
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="feeStructureTypeID" class="col-sm-4 col-form-label pt-0">Recalculate Fees Due based on Total Fees Reported</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="feeStructureTypeID" id="feeStructureTypeID" value="#local.totalFeeStructureTypeID#" class="form-check-input" <cfif local.feeStructureTypeID EQ local.totalFeeStructureTypeID>checked="checked"</cfif> onchange="setFeeStructureChange();">
						<label class="form-check-label" for="feeStructureTypeID">Yes</label>
					</div>       
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="" class="col-sm-4 col-form-label pt-0">Referral Fee Structure</label>
				<div class="col-sm-5 align-self-center">
					<div class="table-responsive-sm w-100">
						<table class="table table-sm" id="feeStructureTbl" >
							<thead>
								<tr>
									<th scope="col" class="text-center">Cumulative Charged<br>Amount</th>
									<th scope="col" class="text-center">Fee %</th>
									<cfif NOT local.isFeeStructureTotals>
									<th scope="col" class="text-center">Range Initial<br>Charge Fee %</th>
									</cfif>
									<th scope="col" class="text-center">Tools</th>
								</tr>
							</thead>
							<tbody>
								<cfloop query="local.qryGetPanelFeeStructureLevels">
									<tr id="tr#local.qryGetPanelFeeStructureLevels.currentrow+1#" class="<cfif local.qryGetPanelFeeStructureLevels.currentrow mod 2 eq 0>ev_modern<cfelse>odd_modern</cfif>">
										<td align="center">
											<input name="cumulativeChargedAmount"  id="cumulativeChargedAmount_#local.qryGetPanelFeeStructureLevels.currentrow#" required="false" type="text" size="10" maxlength="10" value="#numberFormat(local.qryGetPanelFeeStructureLevels.cumulativeChargedAmount,"__.__")#" class="feeStructField form-control form-control-sm"/>
										</td>
										<td align="center">
											<input name="cumulativeFeePercent"  id="cumulativeFeePercent_#local.qryGetPanelFeeStructureLevels.currentrow#" required="false" type="text" size="10" maxlength="10" value="#local.qryGetPanelFeeStructureLevels.cumulativeFeePercent#" class="feeStructField form-control form-control-sm"/>
										</td>
										<cfif NOT local.isFeeStructureTotals>
											<td align="center">
												<input name="rangeInitialChargeFeePercent"  id="rangeInitialChargeFeePercent_#local.qryGetPanelFeeStructureLevels.currentrow#" required="false" type="text" size="10" maxlength="10" value="#local.qryGetPanelFeeStructureLevels.rangeInitialChargeFeePercent#" class="feeStructField form-control form-control-sm"/>
											</td>
										<cfelse>
											<input name="rangeInitialChargeFeePercent"  id="rangeInitialChargeFeePercent_#local.qryGetPanelFeeStructureLevels.currentrow#" type="hidden" value="0"/>
										</cfif>
										<td align="center"><cfif local.qryGetPanelFeeStructureLevels.currentrow gt 1><a href="####" class="removeRow" title="Remove this fee structure level.">Remove</a><cfelse><a href="####" class="addRow" title="Add a new fee structure level.">Add</a></cfif></td>
									</tr>					
								</cfloop>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				<div class="form-group row mt-2">
					<label for="purgePanelsBtn" class="col-sm-4 col-form-label pt-0">Remove Panels from System</label>
					<a href="##" id="purgePanelsBtn" class="btn btn-primary btn-sm pt-2" role="button">Select Panels</a>
					<div class="col-sm-8 align-self-center"></div>
				</div>
			</cfif>
		</div>
		<div id="referralCenter" class="tab-pane <cfif local.activeSelectedTab EQ "referralCenter"> active</cfif>">
			<div class="form-group row mt-2" id="dspLessFilingFeeCostsRow">
				<label for="dspLessFilingFeeCosts" class="col-sm-4 col-form-label pt-0">Display Less Filing Fee And Costs?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="dspLessFilingFeeCosts" id="dspLessFilingFeeCosts" class="form-check-input" value="1" <cfif local.qryReferralSettings.dspLessFilingFeeCosts>checked="checked"</cfif> />
						<label class="form-check-label" for="dspLessFilingFeeCosts">Yes</label>
					</div>        
				</div>
			</div>
			<div class="form-group row">
				<label for="feeDeclarationTxt" class="col-sm-4 col-form-label">Fee Declaration Text</label>
				<div class="col-sm-5 align-self-center">
					<input name="feeDeclarationTxt"  id="feeDeclarationTxt" type="text" size="60" maxlength="255" value="#local.qryReferralSettings.feeDeclarationTxt#" class="form-control form-control-sm"/>
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="feeInformationTopTxt" class="col-sm-4 col-form-label pt-0">Referral Center Fee Information - Top Text</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="feeInformationTopTxt" id="feeInformationTopTxt"  rows="5" cols="60" class="form-control form-control-sm"/>#local.qryReferralSettings.feeInformationTopTxt#</textarea>
				</div>
			</div>
			<div class="form-group row mt-2" id="dspForwardingFeeAmountRow">
				<label for="dspForwardingFeeAmount" class="col-sm-4 col-form-label pt-0">Display Forwarding Fee Amount with Fee Information?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="dspForwardingFeeAmount" id="dspForwardingFeeAmount" value="1" class="form-check-input" <cfif local.qryReferralSettings.dspForwardingFeeAmount>checked="checked"</cfif>/>
						<label class="form-check-label" for="dspForwardingFeeAmount">Yes</label>
					</div> 
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="referralStatementInstructions" class="col-sm-4 col-form-label pt-0">Referral Center Statement Instructions</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="referralStatementInstructions" id="referralStatementInstructions" class="form-control form-control-sm" rows="5" cols="60" />#local.qryReferralSettings.referralStatementInstructions#</textarea>
				</div>
			</div>
			<div class="form-group row" id="allowFeeTypesRow">
				<label for="allowFeeTypes" class="col-sm-4 col-form-label pt-0">Allow Fee Type management in Referral Center?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="allowFeeTypes" id="allowFeeTypes" class="form-check-input" value="1" <cfif local.qryReferralSettings.allowFeeTypeMgmt>checked="checked"</cfif> onclick="javascript:ctrlFeeTypeRows();"/>
						<label class="form-check-label" for="allowFeeTypes">Yes</label>
					</div> 
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="monthlyReportTopTxt" class="col-sm-4 col-form-label pt-0">Monthly Report Notification - Top Text</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="monthlyReportTopTxt" id="monthlyReportTopTxt" class="form-control form-control-sm" rows="5" cols="60" />#local.qryReferralSettings.monthlyReportTopTxt#</textarea>
				</div>
			</div>
			<div class="form-group row mt-2">
				<label for="monthlyReportBottomTxt" class="col-sm-4 col-form-label pt-0">Monthly Report Notification - Bottom Text</label>
				<div class="col-sm-5 align-self-center">
					<textarea name="monthlyReportBottomTxt" id="monthlyReportBottomTxt" class="form-control form-control-sm" rows="5" cols="60" />#local.qryReferralSettings.monthlyReportBottomTxt#</textarea>
				</div>
			</div>
			<div class="form-group row d-none" id="feeTypeListRow">
				<div class="col-sm-5 align-self-center offset-sm-4">
					<div class="card card-box">
						<div class="card-header py-1 bg-light justify-content-between">
							Case Fee Types
							<div>
								<button type="button" id="btnAddPanel" name="btnAddPanel" class="btn btn-sm btn-primary" onclick="editFeeType(0);" data-tooltip-class="tooltip-primary"
									data-toggle="tooltip" data-placement="top" data-trigger="hover" data-original-title="Add New Case Fee Type">
									<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
								</button>
							</div>
						</div>
						<div class="card-body p-1">
							<table id="feeTypeTable" class="table table-sm table-striped table-bordered" style="width:100%">
								<thead>
									<tr>
										<th>Referral Fee Types</th>
										<th>Actions</th>
									</tr>
								</thead>
							</table>
						</div>
					</div>                
				</div>
			</div>
			<div class="form-group row mt-2" id="dspMonthReportLinkRow">
				<label for="dspMonthReportLink" class="col-sm-4 col-form-label pt-0">Display Print Monthly Report link?</label>
				<div class="col-sm-5">
					<div class="form-check">
						<input type="checkbox" name="dspMonthReportLink" id="dspMonthReportLink" value="1" class="form-check-input" <cfif local.qryReferralSettings.dspMonthReportLink>checked="checked"</cfif> />
						<label class="form-check-label" for="dspMonthReportLink">Yes</label>
					</div> 
				</div>
			</div>      
		</div>
		<cfif this.feApplicationInstanceID>
			<div id="frontEndReferrals" class="tab-pane <cfif local.activeSelectedTab EQ "frontEndReferrals"> active</cfif>">
				<div class="form-group row align-items-center mb-2">
					<div class="col-sm-4">Field Set for Search Filter</div>
					<div class="col-sm-5">
						#local.strClientSearchFSSelector.html#
					</div>
				</div>
				<div class="form-group row align-items-center mb-2">
					<div class="col-sm-4">Field Set for Results Screen</div>
					<div class="col-sm-5">
						#local.strClientResultsFSSelector.html#
					</div>
				</div>
				<div class="form-group row mt-2">
					<div class="col-sm-12 align-self-center">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='feFormInstructionsContent', fieldlabel='Form Instructions', contentID=local.feFormInstructionsContentID, content=local.feFormInstructionsContent, allowMergeCodes=0,supportsBootstrap=true, allowVersioning=true)#
					</div>
				</div>
				<div class="form-group row mt-3">
					<div class="col-sm-12 align-self-center">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='feLegalDescInstructContent', fieldlabel='Legal Description Instructions', contentID=local.feLegalDescInstructContentID, content=local.feLegalDescInstructContent, allowMergeCodes=0, supportsBootstrap=true,allowVersioning=true)#
					</div>
				</div>
				<div class="form-group row mt-3">
					<div class="col-sm-12 align-self-center">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='fePanelInfoContent', fieldlabel='Panel Selection Instructions', contentID=local.fePanelInfoContentID, content=local.fePanelInfoContent, allowMergeCodes=0, supportsBootstrap=true,allowVersioning=true)#
					</div>
				</div>
				 <div class="form-group row mt-3">
					<div class="col-sm-12 align-self-center">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='feNoResultsInfoContent', fieldlabel='No Results Instructions', contentID=local.feNoResultsInfoContentID, content=local.feNoResultsInfoContent, allowMergeCodes=0, supportsBootstrap=true,allowVersioning=true)#
					</div>
				</div>
				<div class="form-group row">
					<label for="feCounselorID" class="col-sm-4 col-form-label">Counselor Account used for Referrals</label>
					<div class="col-sm-5 align-self-center">
						<input type="hidden" name="feCounselorID" id="feCounselorID" value="#local.feCounselorID#" />
						<div id="feCounselorIDVal" class="float-left d-inline mr-4">#local.feCounselorName#</div> 
						<div id="feCounselorIDSelect" class="float-left d-inline mr-2"><a href="javascript:selectMemberCF('feCounselorID','Select Member Account')">Select Member</a></div>
					</div>
				</div>
				<div class="form-group row">
					<label for="feMaxMemberRefNum" class="col-sm-4 col-form-label">Maximum number of Members per Referral</label>
					<div class="col-sm-5 align-self-center">
						<input type="text" name="feMaxMemberRefNum" id="feMaxMemberRefNum" value="#local.feMaxMemberRefNum#" class="form-control form-control-sm"/>
					</div>
				</div>
				<div class="form-group row">
					<label for="fePendingStatusID" class="col-sm-4 col-form-label">Front-End Pending Status</label>
					<div class="col-sm-5 align-self-center">
						<select name="fePendingStatusID" id="fePendingStatusID" class="form-control form-control-sm">
							<option value="">Choose a Pending Status</option>
							<cfloop query="local.qryFrontEndPendingStatuses">
								<option value="#local.qryFrontEndPendingStatuses.clientReferralStatusID#" <cfif local.fePendingStatusID eq local.qryFrontEndPendingStatuses.clientReferralStatusID>selected</cfif>>#local.qryFrontEndPendingStatuses.statusName#</option>
							</cfloop>
						</select>
					</div>
				</div>
				<div class="form-group row">
					<label for="fePendingStatusID" class="col-sm-4 col-form-label">Control Panel Pending Status</label>
					<div class="col-sm-5 align-self-center">
						<select name="cpPendingStatusID" id="cpPendingStatusID" class="form-control form-control-sm">
							<option value="">Choose a Pending Status</option>
							<cfloop query="local.qryControlPanelPendingStatuses">
								<option value="#local.qryControlPanelPendingStatuses.clientReferralStatusID#" <cfif val(local.qryReferralSettings.cpPendingStatusID) eq local.qryControlPanelPendingStatuses.clientReferralStatusID>selected</cfif>>#local.qryControlPanelPendingStatuses.statusName#</option>
							</cfloop>
						</select>
					</div>
				</div>
				<div class="form-group row">
					<label for="feReferredStatusID" class="col-sm-4 col-form-label">Front-End Referred Status</label>
					<div class="col-sm-5 align-self-center">
						<select name="feReferredStatusID" id="feReferredStatusID" class="form-control form-control-sm">
							<option value="">Choose a Pending Status</option>
							<cfloop query="local.qryFrontEndReferredStatuses">
								<option value="#local.qryFrontEndReferredStatuses.clientReferralStatusID#" <cfif local.feReferredStatusID eq local.qryFrontEndReferredStatuses.clientReferralStatusID>selected</cfif>>#local.qryFrontEndReferredStatuses.statusName#</option>
							</cfloop>
						</select>
					</div>
				</div>
				<div class="form-group row">
					<label for="feMerchantProfileID" class="col-sm-4 col-form-label">Front-End Payment Profile</label>
					<div class="col-sm-5 align-self-center">
						<select name="feMerchantProfileID" id="feMerchantProfileID" class="form-control form-control-sm">
							<option value="">Choose Payment Profile</option>
							<cfloop query="local.qryGetAllPaymentProfiles">
								<option value="#local.qryGetAllPaymentProfiles.profileID#" <cfif listFind(local.fePayProfileID,local.qryGetAllPaymentProfiles.profileID)>selected="selected"</cfif>>#local.qryGetAllPaymentProfiles.profileName#</option>
							</cfloop>
						</select>
					</div>
				</div>
				<div class="form-group">
					<label class="font-weight-bold">Default Front-End Client Fees Revenue Account*:</label>
					#local.strFEGLAcctWidget.html#
				</div>
				<div class="form-group row mt-2" id="feDspSurveyOptionWrapper">
					<label for="feDspSurveyOption" class="col-sm-4 col-form-label pt-0">Display Survey Option in Front-End?</label>
					<div class="col-sm-5">
						<div class="form-check">
							<input type="checkbox" name="feDspSurveyOption" id="feDspSurveyOption" value="1" class="form-check-input" <cfif local.feDspSurveyOption>checked="checked"</cfif> />
							<label class="form-check-label" for="feDspSurveyOption">Yes</label>
						</div> 
					</div>
				</div>
				<div class="form-group row mt-2" id="feSurveyOptionDefaultYesWrapper">
					<label for="feSurveyOptionDefaultYes" class="col-sm-4 col-form-label pt-0">Default Survey Option to Yes?</label>
					<div class="col-sm-5">
						<div class="form-check">
							<input type="checkbox" name="feSurveyOptionDefaultYes" id="feSurveyOptionDefaultYes" value="1" class="form-check-input" <cfif local.feSurveyOptionDefaultYes>checked="checked"</cfif> />
							<label class="form-check-label" for="feSurveyOptionDefaultYes">Yes</label>
						</div> 
					</div>
				</div>
				<div class="form-group row mt-2" id="feDspBlogOptionWrapper">
					<label for="feDspBlogOption" class="col-sm-4 col-form-label pt-0">Display Blog Option in Front-End?</label>
					<div class="col-sm-5">
						<div class="form-check">
							<input type="checkbox" name="feDspBlogOption" id="feDspBlogOption" value="1" class="form-check-input" <cfif local.feDspBlogOption>checked="checked"</cfif> />
							<label class="form-check-label" for="feDspBlogOption">Yes</label>
						</div> 
					</div>
				</div>
				<div class="form-group row mt-2">
					<label for="hideRepFieldsFE" class="col-sm-4 col-form-label pt-0">Hide Representative Fields from the Front-End?</label>
					<div class="col-sm-5">
						<div class="form-check">
							<input type="checkbox" name="hideRepFieldsFE" id="hideRepFieldsFE" value="1" class="form-check-input" <cfif val(local.qryClientReferralSettings.hideRepFieldsFE)>checked="checked"</cfif> />
							<label class="form-check-label" for="hideRepFieldsFE">Yes</label>
						</div> 
					</div>
				</div>
				<div class="form-group row mt-2" id="feDspQuestionTreeWrapper">
					<label for="feDspQuestionTree" class="col-sm-4 col-form-label pt-0">Display Question Tree in Front-End?</label>
					<div class="col-sm-5">
						<div class="form-check">
							<input type="checkbox" name="feDspQuestionTree" id="feDspQuestionTree" value="1" class="form-check-input" <cfif local.feDspQuestionTree>checked="checked"</cfif> />
							<label class="form-check-label" for="feDspQuestionTree">Yes</label>
						</div> 
					</div>
				</div>
				<div class="form-group row questionRelated mt-2" id="feDspClientInfoFormFirstWrapper">
					<label for="feDspClientInfoFormFirst" class="col-sm-4 col-form-label pt-0">Display Client Information Form at the beginning in Front-End?</label>
					<div class="col-sm-5">
						<div class="form-check">
							<input type="checkbox" name="feDspClientInfoFormFirst" id="feDspClientInfoFormFirst" value="1" class="form-check-input" <cfif local.feDspClientInfoFormFirst>checked="checked"</cfif> />
							<label class="form-check-label" for="feDspClientInfoFormFirst">Yes</label>
						</div> 
					</div>
				</div>
				<div class="form-group row questionRelated mt-2">
					<div class="col-sm-12 align-self-center">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='feFormInstructionsStep1Content', fieldlabel='Form Instructions - Panel Selection', contentID=local.feFormInstructionsStep1ContentID, content=local.feFormInstructionsStep1Content, allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)# 
					</div>
				</div>
				<div class="form-group row questionRelated mt-3">
					<div class="col-sm-12 align-self-center">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='feFormInstructionsStep2Content', fieldlabel='Form Instructions - Referral Information Match', contentID=local.feFormInstructionsStep2ContentID, content=local.feFormInstructionsStep2Content, allowMergeCodes=0, supportsBootstrap=true,allowVersioning=true)#
					</div>
				</div>
				<div class="form-group row questionRelated mt-3">
					<div class="col-sm-12 align-self-center">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='feFormInstructionsStep3Content', fieldlabel='Form Instructions - Contact Information', contentID=local.feFormInstructionsStep3ContentID, content=local.feFormInstructionsStep3Content, allowMergeCodes=0, supportsBootstrap=true,allowVersioning=true)#
					</div>
				</div>
				<div class="form-group row questionRelated mt-3">
					<div class="col-sm-12 align-self-center">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='feFormInstructionsStep4Content', fieldlabel='Form Instructions - Referral Results', contentID=local.feFormInstructionsStep4ContentID, content=local.feFormInstructionsStep4Content, allowMergeCodes=0, supportsBootstrap=true,allowVersioning=true)#
					</div>
				</div>
				<div class="form-group row questionRelated">
					<label for="feLegalIssueDescTitle" class="col-sm-4 col-form-label">Legal Issue Description Section Title</label>
					<div class="col-sm-5 align-self-center">
						<input name="feLegalIssueDescTitle"  id="feLegalIssueDescTitle" type="text" size="60" class="form-control form-control-sm" maxlength="255" value="#local.qryReferralSettings.feLegalIssueDescTitle#" />
					</div>
				</div>
				<div class="form-group row questionRelated">
					<label for="feAdditionalFiltersTitle" class="col-sm-4 col-form-label">Additional Filters Section Title</label>
					<div class="col-sm-5 align-self-center">
						<input name="feAdditionalFiltersTitle"  id="feAdditionalFiltersTitle" type="text" size="60" class="form-control form-control-sm" maxlength="255" value="#local.qryReferralSettings.feAdditionalFiltersTitle#" /> 
					</div>
				</div>
				<div class="form-group row questionRelated">
					<label for="feReferralInforMatchTitle" class="col-sm-4 col-form-label">Referral Information Match Section Title</label>
					<div class="col-sm-5 align-self-center">
						<input name="feReferralInforMatchTitle"  id="feReferralInforMatchTitle" type="text" size="60" class="form-control form-control-sm" maxlength="255" value="#local.qryReferralSettings.feReferralInforMatchTitle#" />        
					</div>
				</div>
				<div class="form-group row questionRelated">
					<label for="feAdditionalInfoTitle" class="col-sm-4 col-form-label">Additional Information Section Title</label>
					<div class="col-sm-5 align-self-center">
						<input name="feAdditionalInfoTitle"  id="feAdditionalInfoTitle" type="text" size="60" class="form-control form-control-sm" maxlength="255" value="#local.qryReferralSettings.feAdditionalInfoTitle#" />        
					</div>
				</div>
				<div class="form-group row questionRelated mt-3">
					<div class="col-sm-12 align-self-center">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='feSuccessfulResultsInstructionsContent', fieldlabel='Successful Results Instructions', contentID=local.feSuccessfulResultsInstructionsContentID, content=local.feSuccessfulResultsInstructionsContent, allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
					</div>
				</div>	
			</div>
		</cfif>
		<cfif this.referralsSMS>
			<div id="frontEndReferralsSMS" class="tab-pane <cfif local.activeSelectedTab EQ "frontEndReferralsSMS"> active</cfif>">
				<cfinclude template="dsp_referralTextMessages.cfm">	
			</div>
		</cfif>
		<div id="intakeManagement" class="tab-pane <cfif local.activeSelectedTab EQ "intakeManagement"> active</cfif>">
			<cfinclude template="frm_intakeManagement.cfm">
		</div>        
		<div id="emailTemplates" class="tab-pane <cfif local.activeSelectedTab EQ "emailTemplates"> active</cfif>">
			<cfinclude template="dsp_referralEmailTemplates.cfm">
		</div>
	</div>
</form>
</cfoutput>