<cfsavecontent variable="local.widgetHead">
	<cfoutput>
	<script type="text/javascript">
		function togglePmtMethodSelector#arguments.selectorID#(card) {
			if ($(card).hasClass('disabled')) return;

			const pmtMethodSelector = $(card).find('input[type="checkbox"]');
			const isChecked = pmtMethodSelector.prop('checked');
			pmtMethodSelector.prop('checked', !isChecked);
			$(card).toggleClass('active', !isChecked);

			initPmtMethodSelector#arguments.selectorID#();
			<cfif len(arguments.onChangeFuncName)>#arguments.onChangeFuncName#();</cfif>
		}
		function initPmtMethodSelector#arguments.selectorID#() {
			let arrPmtMethods = [];
			$('###arguments.selectorID#Container').find('.mc-cbox-card-grid').addClass('noaction');
			$('###arguments.selectorID#Container').find('.mc-cbox-card-grid').removeClass('disabled');
			$('input[name="#arguments.selectorID#"]:checked').each(function() {
				let mpID = $(this).val();
				let tokenSource = $(this).data('tokenstore');
				if(tokenSource !='offlinecash'){
					$('input[name="#arguments.selectorID#"][data-tokenstore="'+tokenSource+'"]')
						.not('[value="' + mpID + '"]')
						.not('[data-locked="true"]')
						.closest('.mc-cbox-card-grid')
						.not('.active')
						.addClass('disabled');
				}	
				arrPmtMethods.push($(this).parent().find('.mc-cbox-label').text());
			});
			$('###arguments.selectorID#Container').find('.mc-cbox-card-grid').removeClass('noaction');
			$('##selectedPmtMethods#arguments.selectorID#').html(arrPmtMethods.length ? arrPmtMethods.join(', ') : 'Choose Payment Methods');
		}

		$(function() {
			initPmtMethodSelector#arguments.selectorID#();
		});
	</script>
	<style type="text/css">
		.mc-cbox-card-grid {border:2px solid ##dee2e6;border-radius:4px;padding:0.5rem;transition:border-color 0.3s, background-color 0.3s;cursor:pointer;position:relative;}
		.mc-cbox-card-grid.active {border-color:##007bff;background-color:##f0f8ff;}
		.mc-cbox-card-grid.disabled {background-color: ##e9ecef;border-color: ##ccc;color:##aaa;cursor:not-allowed;pointer-events:none;}
		.mc-cbox-card-grid input[type="checkbox"], .mc-cbox-card-grid .mc-cbox-ban {position:absolute;top:10px;right:10px;transform:scale(1.3);pointer-events:none;}
		.mc-cbox-card-grid.disabled .mc-cbox-label {color:##aaa;}
		.mc-cbox-card-grid.disabled input[type="checkbox"] {display:none;}
		.mc-cbox-card-grid .mc-cbox-ban {display:none;}
		.mc-cbox-card-grid.disabled .mc-cbox-ban {display:inline-block;}
		.mc-cbox-card-grid.noaction {pointer-events:none;}
		###arguments.selectorID#Container .btn:disabled{color:##434343;}
		<cfif len(arguments.fieldLabel)>
		##accordion_#arguments.selectorID# ##heading_#arguments.selectorID#.card-header .field-header-label {
			position: absolute;
			top: 0.25rem;
			left: .5rem;
			font-size: 0.75rem;
			color: ##6c757d;
			padding: 0 0.25rem;
			pointer-events: none; /* don't block clicks */
			z-index: 2;
		}
		##accordion_#arguments.selectorID# ##heading_#arguments.selectorID#.card-header span.field-header-label + .btn-link span {
			margin-top: .8rem;
		}
		</cfif>
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.widgetHead#">

<cfoutput>
<cfif len(arguments.title)><h6>#arguments.title#</h6></cfif>
<cfif len(arguments.desc)><div class="small text-dim">#arguments.desc#</div></cfif>
<div id="#arguments.selectorID#Container" class="mb-3">
	<div class="accordion" id="accordion_#arguments.selectorID#">
		<div class="card card-box rounded">
			<div class="card-header p-0 bg-secondary" id="heading_#arguments.selectorID#">
				<cfif len(arguments.fieldLabel)>
					<span class="field-header-label">#arguments.fieldLabel#</span>
				</cfif>
				<button class="btn btn-link d-flex align-items-center justify-content-between collapsed" type="button" data-toggle="collapse" data-target="##collapse_#arguments.selectorID#" aria-expanded="false" aria-controls="collapse_#arguments.selectorID#"<cfif arguments.readOnly> disabled</cfif>>
					<span id="selectedPmtMethods#arguments.selectorID#">Choose Payment Methods</span>
					<i class="fa-solid fa-caret-up font-size-xl"></i>
				</button>
			</div>
			<div id="collapse_#arguments.selectorID#" class="collapse" aria-labelledby="heading_#arguments.selectorID#" data-parent="##accordion_#arguments.selectorID#">
				<div class="card-body p-3" style="max-height:250px;overflow-y:auto;">
					<div class="form-row">
						<cfloop query="local.qryAvailableMerchantProfiles">
							<cfset local.thisPmtProfileSelected = len(arguments.selectedPaymentProfileIDList) AND listFind(arguments.selectedPaymentProfileIDList,local.qryAvailableMerchantProfiles.profileID) ? true : false>
							<cfset local.thisPmtProfileLocked = len(arguments.lockedProfileIDList) AND listFind(arguments.lockedProfileIDList,local.qryAvailableMerchantProfiles.profileID) ? true : false>
							<div class="col-#12/arguments.colCount# mb-1">
								<div class="mc-cbox-card-grid<cfif local.thisPmtProfileSelected> active</cfif>"<cfif NOT local.thisPmtProfileLocked> onclick="togglePmtMethodSelector#arguments.selectorID#(this);"</cfif>>
									<input type="checkbox" name="#arguments.selectorID#" value="#local.qryAvailableMerchantProfiles.profileID#" data-tokenstore="#lCase(local.qryAvailableMerchantProfiles.tokenStore)#"<cfif local.thisPmtProfileSelected> checked</cfif><cfif local.thisPmtProfileLocked> data-locked="true"</cfif> autocomplete="off">
									<i class="fa-solid fa-ban mc-cbox-ban"></i>
									<div class="font-size-sm mb-1 mc-cbox-label">#local.qryAvailableMerchantProfiles.profileName#</div>
									<div class="small text-dim">#local.qryAvailableMerchantProfiles.gatewayTypeDesc#</div>
								</div>
							</div>
						</cfloop>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>