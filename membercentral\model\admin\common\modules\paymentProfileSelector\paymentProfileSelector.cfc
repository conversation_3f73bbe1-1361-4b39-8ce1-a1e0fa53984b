<cfcomponent output="false">

	<cffunction name="getPaymentProfileSelector" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="selectorID" type="string" required="true" hint="id for input control">
		<cfargument name="selectedPaymentProfileIDList" type="string" required="false" default="" hint="selected profile ids">
		<cfargument name="title" type="string" required="false" default="Payment Methods" hint="title">
		<cfargument name="desc" type="string" required="false" default="" hint="description">
		<cfargument name="colCount" type="string" required="false" default="3" hint="number of payment profiles in a row">
		<cfargument name="readOnly" type="boolean" required="false" default="false" hint="readonly widget">
		<cfargument name="allowOffline" type="boolean" required="false" default="false" hint="whether to allow Offline gateways - offlineCash, AAJImport, AcctImport">
		<cfargument name="allowPayLater" type="boolean" required="false" default="true" hint="whether to allow PayLater gateway">
		<cfargument name="onChangeFuncName" type="string" required="false" default="" hint="onchange function name">
		<cfargument name="lockedProfileIDList" type="string" required="false" default="" hint="locked profile cards">
		<cfargument name="fieldLabel" type="string" required="false" default="" hint="optional floating label for field">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'selectorID':arguments.selectorID, 'html':'' }>
		<cfset local.qryAvailableMerchantProfiles = getAvailableMerchantProfiles(siteID=arguments.siteID, allowOffline=arguments.allowOffline, allowPayLater=arguments.allowPayLater, includeProfileIDList=arguments.lockedProfileIDList)>

		<cfsavecontent variable="local.returnStruct.html">
			<cfinclude template="dsp_paymentProfileSelector.cfm">
		</cfsavecontent>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAvailableMerchantProfiles" access="private" output="false" returntype="query" hint="Get the available merchant profiles">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="allowOffline" type="boolean" required="true">
		<cfargument name="allowPayLater" type="boolean" required="true">
		<cfargument name="includeProfileIDList" type="string" required="true">

		<cfset var qryAvailableMerchantProfiles = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryAvailableMerchantProfiles">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			DECLARE @tmpMPProfiles TABLE (profileID int PRIMARY KEY, profileName varchar(100), gatewayTypeDesc varchar(100), tokenStore varchar(30));

			INSERT INTO @tmpMPProfiles (profileID, profileName, gatewayTypeDesc, tokenStore)
			SELECT mp.profileID, mp.profileName, g.gatewayTypeDesc, g.tokenStore
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways AS g ON mp.gatewayID = g.gatewayID
				AND g.isActive = 1
				<cfif NOT arguments.allowOffline>
					AND g.gatewayType not in ('OfflineCash','AcctImport','AAJImport')
				</cfif>
				<cfif NOT arguments.allowPayLater>
					AND g.gatewayType <> 'PayLater'
				</cfif>
			WHERE mp.siteID = @siteID
			AND mp.status = 'A'
			AND mp.allowPayments = 1;

			<!--- MUST include these profiles --->
			<cfif len(arguments.includeProfileIDList)>
				INSERT INTO @tmpMPProfiles (profileID, profileName, gatewayTypeDesc, tokenStore)
				SELECT mp.profileID, mp.profileName, g.gatewayTypeDesc, g.tokenStore
				FROM dbo.mp_profiles AS mp
				INNER JOIN dbo.mp_gateways AS g ON mp.gatewayID = g.gatewayID
					AND g.isActive = 1
				LEFT OUTER JOIN @tmpMPProfiles AS tmp ON tmp.profileID = mp.profileID
				WHERE mp.siteID = @siteID
				AND mp.status IN ('A','I')
				AND mp.profileID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.includeProfileIDList#">)
				AND tmp.profileID IS NULL;
			</cfif>

			SELECT profileID, profileName, gatewayTypeDesc, tokenStore
			FROM @tmpMPProfiles
			ORDER BY profileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryAvailableMerchantProfiles>
	</cffunction>

</cfcomponent>